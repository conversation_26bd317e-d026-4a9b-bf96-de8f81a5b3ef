import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Textarea } from './ui/textarea';
import { Checkbox } from './ui/checkbox';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { Flame, AlertTriangle, Heart, Clock } from 'lucide-react';

interface VoteCustomization {
  spiceLevel: string;
  allergies: string[];
  specialRequests: string;
  dietaryPreferences: string[];
  cookingPreferences: string[];
}

interface VoteCustomizationFormProps {
  onSubmit: (customization: VoteCustomization) => void;
  onCancel: () => void;
  isLoading?: boolean;
  dishName: string;
  price: string;
}

const SPICE_LEVELS = [
  { value: 'mild', label: 'Mild', description: 'No spice, perfect for everyone' },
  { value: 'medium', label: 'Medium', description: 'Balanced heat, most popular' },
  { value: 'hot', label: 'Hot', description: 'Spicy kick, for heat lovers' },
  { value: 'extra-hot', label: 'Extra Hot', description: 'Very spicy, proceed with caution' }
];

const COMMON_ALLERGIES = [
  'Peanuts', 'Tree Nuts', 'Dairy', 'Eggs', 'Soy', 'Wheat', 'Fish', 'Shellfish'
];

const DIETARY_PREFERENCES = [
  { value: 'vegetarian', label: 'Vegetarian', icon: '🌱' },
  { value: 'vegan', label: 'Vegan', icon: '🌿' },
  { value: 'gluten-free', label: 'Gluten Free', icon: '🌾' },
  { value: 'low-sodium', label: 'Low Sodium', icon: '🧂' }
];

const COOKING_PREFERENCES = [
  { value: 'well-done', label: 'Well Done', description: 'Fully cooked through' },
  { value: 'medium-well', label: 'Medium Well', description: 'Slightly pink center' },
  { value: 'medium', label: 'Medium', description: 'Pink center, juicy' },
  { value: 'medium-rare', label: 'Medium Rare', description: 'Red center, very juicy' }
];

export const VoteCustomizationForm: React.FC<VoteCustomizationFormProps> = ({
  onSubmit,
  onCancel,
  isLoading = false,
  dishName,
  price
}) => {
  const [customization, setCustomization] = useState<VoteCustomization>({
    spiceLevel: 'medium',
    allergies: [],
    specialRequests: '',
    dietaryPreferences: [],
    cookingPreferences: []
  });

  const [newAllergy, setNewAllergy] = useState('');

  const handleAllergyToggle = (allergy: string) => {
    setCustomization(prev => ({
      ...prev,
      allergies: prev.allergies.includes(allergy)
        ? prev.allergies.filter(a => a !== allergy)
        : [...prev.allergies, allergy]
    }));
  };

  const addCustomAllergy = () => {
    if (newAllergy.trim() && !customization.allergies.includes(newAllergy.trim())) {
      setCustomization(prev => ({
        ...prev,
        allergies: [...prev.allergies, newAllergy.trim()]
      }));
      setNewAllergy('');
    }
  };

  const handleDietaryToggle = (preference: string) => {
    setCustomization(prev => ({
      ...prev,
      dietaryPreferences: prev.dietaryPreferences.includes(preference)
        ? prev.dietaryPreferences.filter(p => p !== preference)
        : [...prev.dietaryPreferences, preference]
    }));
  };

  const handleCookingToggle = (preference: string) => {
    setCustomization(prev => ({
      ...prev,
      cookingPreferences: prev.cookingPreferences.includes(preference)
        ? prev.cookingPreferences.filter(p => p !== preference)
        : [...prev.cookingPreferences, preference]
    }));
  };

  const handleSubmit = () => {
    onSubmit(customization);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Customize Your Vote</span>
          <Badge variant="outline" className="text-lg font-semibold">
            ${price}
          </Badge>
        </CardTitle>
        <p className="text-sm text-gray-600">
          Personalize your vote for <span className="font-semibold">{dishName}</span>
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Spice Level */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2">
            <Flame className="h-4 w-4 text-orange-500" />
            Spice Level
          </Label>
          <Select 
            value={customization.spiceLevel} 
            onValueChange={(value) => setCustomization(prev => ({ ...prev, spiceLevel: value }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {SPICE_LEVELS.map((level) => (
                <SelectItem key={level.value} value={level.value}>
                  <div className="flex flex-col">
                    <span className="font-medium">{level.label}</span>
                    <span className="text-xs text-gray-500">{level.description}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Separator />

        {/* Allergies */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            Allergies & Dietary Restrictions
          </Label>
          <div className="grid grid-cols-2 gap-2">
            {COMMON_ALLERGIES.map((allergy) => (
              <div key={allergy} className="flex items-center space-x-2">
                <Checkbox
                  id={allergy}
                  checked={customization.allergies.includes(allergy)}
                  onCheckedChange={() => handleAllergyToggle(allergy)}
                />
                <Label htmlFor={allergy} className="text-sm">{allergy}</Label>
              </div>
            ))}
          </div>
          
          {/* Custom Allergy Input */}
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Add custom allergy..."
              value={newAllergy}
              onChange={(e) => setNewAllergy(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
              onKeyPress={(e) => e.key === 'Enter' && addCustomAllergy()}
            />
            <Button 
              type="button" 
              variant="outline" 
              size="sm"
              onClick={addCustomAllergy}
              disabled={!newAllergy.trim()}
            >
              Add
            </Button>
          </div>

          {/* Selected Allergies */}
          {customization.allergies.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {customization.allergies.map((allergy) => (
                <Badge 
                  key={allergy} 
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  {allergy}
                  <button
                    onClick={() => handleAllergyToggle(allergy)}
                    className="ml-1 hover:text-red-500"
                  >
                    ×
                  </button>
                </Badge>
              ))}
            </div>
          )}
        </div>

        <Separator />

        {/* Dietary Preferences */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2">
            <Heart className="h-4 w-4 text-green-500" />
            Dietary Preferences
          </Label>
          <div className="grid grid-cols-2 gap-2">
            {DIETARY_PREFERENCES.map((pref) => (
              <div key={pref.value} className="flex items-center space-x-2">
                <Checkbox
                  id={pref.value}
                  checked={customization.dietaryPreferences.includes(pref.value)}
                  onCheckedChange={() => handleDietaryToggle(pref.value)}
                />
                <Label htmlFor={pref.value} className="text-sm">
                  {pref.icon} {pref.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Cooking Preferences */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-blue-500" />
            Cooking Preferences
          </Label>
          <div className="grid grid-cols-1 gap-2">
            {COOKING_PREFERENCES.map((pref) => (
              <div key={pref.value} className="flex items-center space-x-2">
                <Checkbox
                  id={pref.value}
                  checked={customization.cookingPreferences.includes(pref.value)}
                  onCheckedChange={() => handleCookingToggle(pref.value)}
                />
                <Label htmlFor={pref.value} className="text-sm">
                  <span className="font-medium">{pref.label}</span>
                  <span className="text-gray-500 ml-2">- {pref.description}</span>
                </Label>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Special Requests */}
        <div className="space-y-3">
          <Label>Special Requests</Label>
          <Textarea
            placeholder="Any special instructions for the chef? (optional)"
            value={customization.specialRequests}
            onChange={(e) => setCustomization(prev => ({ ...prev, specialRequests: e.target.value }))}
            rows={3}
            maxLength={200}
          />
          <div className="text-xs text-gray-500 text-right">
            {customization.specialRequests.length}/200 characters
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button 
            variant="outline" 
            onClick={onCancel}
            disabled={isLoading}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={isLoading}
            className="flex-1"
          >
            {isLoading ? 'Submitting Vote...' : `Vote $${price}`}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}; 