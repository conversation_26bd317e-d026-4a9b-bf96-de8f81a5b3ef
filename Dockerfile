# Use Node.js 20 Alpine for smaller image size
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build only the frontend application (skip TypeScript compilation for now)
RUN npm run build

# Production image, copy all the files and run the app
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV PORT=5000

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 cloudkitchen

# Copy the built frontend
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# Copy the simple server for production
COPY --from=builder /app/simple-server.cjs ./simple-server.cjs

# Create necessary directories with proper permissions
RUN mkdir -p /app/dist && chown -R cloudkitchen:nodejs /app

USER cloudkitchen

EXPOSE 5000

ENV PORT=5000
ENV HOSTNAME="0.0.0.0"

# Use the simple server instead of the complex TypeScript build
CMD ["node", "simple-server.cjs"]