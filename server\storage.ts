import { users, dishes, votes, orders, dishCategories, type User, type InsertUser, type Dish, type InsertDish, type DishCategory, type InsertDishCategory, type Vote, type InsertVote, type Order, type InsertOrder, type DishWithChef, type OrderWithDetails, type DishFilter, type DishSearchParams } from "../shared/schema";
import bcrypt from 'bcryptjs';

export interface IStorage {
  // Users
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  getUsersByRole(role: string): Promise<User[]>;
  updateUserRating(userId: number, rating: number): Promise<void>;
  verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean>;

  // Dishes
  getDish(id: number): Promise<Dish | undefined>;
  getDishes(): Promise<Dish[]>;
  getDishesByChef(chefId: number): Promise<Dish[]>;
  getDishesWithChef(): Promise<DishWithChef[]>;
  createDish(dish: InsertDish): Promise<Dish>;
  updateDish(dishId: number, dishData: Partial<InsertDish>): Promise<Dish>;
  deleteDish(dishId: number): Promise<void>;
  updateDishStatus(dishId: number, status: string): Promise<void>;
  updateDishVotes(dishId: number, votes: number): Promise<void>;
  searchDishes(params: DishSearchParams): Promise<Dish[]>;
  getDishesByCategory(category: string): Promise<Dish[]>;
  getDishesByRegion(region: string): Promise<Dish[]>;
  getDishesByTags(tags: string[]): Promise<Dish[]>;

  // Dish Categories
  getDishCategories(): Promise<DishCategory[]>;
  createDishCategory(category: InsertDishCategory): Promise<DishCategory>;
  updateDishCategory(id: number, category: Partial<InsertDishCategory>): Promise<DishCategory>;
  deleteDishCategory(id: number): Promise<void>;

  // Votes
  createVote(vote: InsertVote): Promise<Vote>;
  getVotesByDish(dishId: number): Promise<Vote[]>;
  hasUserVoted(userId: number, dishId: number): Promise<boolean>;

  // Orders
  createOrder(order: InsertOrder): Promise<Order>;
  getOrders(): Promise<Order[]>;
  getOrdersByUser(userId: number): Promise<Order[]>;
  getOrdersByChef(chefId: number): Promise<Order[]>;
  getOrdersByDish(dishId: number): Promise<Order[]>;
  getOrdersWithDetails(): Promise<OrderWithDetails[]>;
  updateOrderStatus(orderId: number, status: string): Promise<void>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private dishes: Map<number, Dish>;
  private votes: Map<number, Vote>;
  private orders: Map<number, Order>;
  private dishCategories: Map<number, DishCategory>;
  private currentUserId: number;
  private currentDishId: number;
  private currentVoteId: number;
  private currentOrderId: number;
  private currentCategoryId: number;

  constructor() {
    this.users = new Map();
    this.dishes = new Map();
    this.votes = new Map();
    this.orders = new Map();
    this.dishCategories = new Map();
    this.currentUserId = 1;
    this.currentDishId = 1;
    this.currentVoteId = 1;
    this.currentOrderId = 1;
    this.currentCategoryId = 1;

    this.initializeData();
  }

  private async initializeData() {
    // Create sample chefs with hashed passwords
    const chefs = [
      { username: "oma_m", name: "Oma M.", email: "<EMAIL>", password: "password", role: "chef", region: "Manipur", rating: "4.9", totalOrders: 127, avatar: "https://images.unsplash.com/photo-1595273670150-bd0c3c392e46?ixlib=rb-4.0.3&w=100&h=100&fit=crop&crop=face" },
      { username: "kiran_m", name: "Kiran M.", email: "<EMAIL>", password: "password", role: "chef", region: "Nagaland", rating: "4.8", totalOrders: 93, avatar: "https://images.unsplash.com/photo-1607631568010-a87245c0daf8?ixlib=rb-4.0.3&w=100&h=100&fit=crop&crop=face" },
      { username: "priya_s", name: "Priya S.", email: "<EMAIL>", password: "password", role: "chef", region: "Bihar", rating: "4.7", totalOrders: 85, avatar: "https://images.unsplash.com/photo-1664575602276-acd073f104c1?ixlib=rb-4.0.3&w=100&h=100&fit=crop&crop=face" },
      { username: "awan_t", name: "Awan T.", email: "<EMAIL>", password: "password", role: "chef", region: "Arunachal Pradesh", rating: "4.9", totalOrders: 76, avatar: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&w=100&h=100&fit=crop&crop=face" }
    ];

    for (const chef of chefs) {
      const hashedPassword = await bcrypt.hash(chef.password, 12);
      const user: User = { ...chef, password: hashedPassword, id: this.currentUserId++, isActive: true };
      this.users.set(user.id, user);
    }

    // Create dish categories
    const categories = [
      { name: "Main Course", description: "Primary dishes", icon: "🍽️", color: "#FF6B6B" },
      { name: "Appetizer", description: "Starters and snacks", icon: "🥨", color: "#4ECDC4" },
      { name: "Dessert", description: "Sweet treats", icon: "🍰", color: "#45B7D1" },
      { name: "Beverage", description: "Drinks and refreshments", icon: "🥤", color: "#96CEB4" },
      { name: "Side Dish", description: "Accompaniments", icon: "🥗", color: "#FFEAA7" }
    ];

    for (const category of categories) {
      const dishCategory: DishCategory = { ...category, id: this.currentCategoryId++, isActive: true };
      this.dishCategories.set(dishCategory.id, dishCategory);
    }

    // Create sample dishes with enhanced data
    const sampleDishes = [
      { 
        chefId: 1, 
        name: "Iromba", 
        description: "Traditional Manipuri fermented fish curry with fresh vegetables", 
        price: "12.50", 
        image: "https://images.unsplash.com/photo-1603133872878-684f208fb84b?ixlib=rb-4.0.3&w=400&h=250&fit=crop", 
        region: "Manipur", 
        cookingTime: 45, 
        minOrders: 35, 
        currentVotes: 25, 
        category: "Main Course",
        tags: ["traditional", "fermented", "fish", "vegetables"],
        status: "active",
        ingredients: ["fish", "vegetables", "fermented bamboo", "herbs"],
        allergens: ["fish"],
        dietaryInfo: { vegetarian: false, vegan: false, glutenFree: true, spicy: true }
      },
      { 
        chefId: 2, 
        name: "Smoked Pork with Bamboo", 
        description: "Authentic Nagaland delicacy with bamboo shoots", 
        price: "15.00", 
        image: "https://images.unsplash.com/photo-1529042410759-befb1204b468?ixlib=rb-4.0.3&w=400&h=250&fit=crop", 
        region: "Nagaland", 
        cookingTime: 60, 
        minOrders: 25, 
        currentVotes: 18, 
        category: "Main Course",
        tags: ["smoked", "pork", "bamboo", "authentic"],
        status: "active",
        ingredients: ["pork", "bamboo shoots", "smoke", "spices"],
        allergens: [],
        dietaryInfo: { vegetarian: false, vegan: false, glutenFree: true, spicy: false }
      },
      { 
        chefId: 4, 
        name: "Sido Chure", 
        description: "Fermented rice delicacy from Arunachal Pradesh", 
        price: "10.00", 
        image: "https://images.unsplash.com/photo-1603133872878-684f208fb84b?ixlib=rb-4.0.3&w=400&h=250&fit=crop", 
        region: "Arunachal Pradesh", 
        cookingTime: 30, 
        minOrders: 20, 
        currentVotes: 12, 
        category: "Side Dish",
        tags: ["fermented", "rice", "traditional"],
        status: "active",
        ingredients: ["rice", "fermentation agent"],
        allergens: [],
        dietaryInfo: { vegetarian: true, vegan: true, glutenFree: true, spicy: false }
      },
      { 
        chefId: 3, 
        name: "Litti Chokha", 
        description: "Rustic Bihar specialty with sattu filling", 
        price: "8.50", 
        image: "https://images.unsplash.com/photo-1596797038530-2c107229654b?ixlib=rb-4.0.3&w=400&h=250&fit=crop", 
        region: "Bihar", 
        cookingTime: 40, 
        minOrders: 30, 
        currentVotes: 28, 
        category: "Snack",
        tags: ["rustic", "sattu", "traditional"],
        status: "active",
        ingredients: ["sattu", "wheat flour", "vegetables"],
        allergens: ["gluten"],
        dietaryInfo: { vegetarian: true, vegan: true, glutenFree: false, spicy: false }
      },
      { 
        chefId: 1, 
        name: "Singju", 
        description: "Traditional Manipuri salad with herbs and vegetables", 
        price: "8.00", 
        image: "https://images.unsplash.com/photo-1631452180519-c014fe946bc7?ixlib=rb-4.0.3&w=400&h=250&fit=crop", 
        region: "Manipur", 
        cookingTime: 20, 
        minOrders: 20, 
        currentVotes: 20, 
        category: "Salad",
        tags: ["salad", "herbs", "vegetables", "fresh"],
        status: "active",
        ingredients: ["mixed vegetables", "herbs", "sesame seeds"],
        allergens: ["sesame"],
        dietaryInfo: { vegetarian: true, vegan: true, glutenFree: true, spicy: false }
      }
    ];

    for (const dish of sampleDishes) {
      const dishRecord: Dish = { 
        ...dish, 
        id: this.currentDishId++, 
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      this.dishes.set(dishRecord.id, dishRecord);
    }

    // Create admin user with hashed password
    const adminPassword = await bcrypt.hash("admin123", 12);
    const admin: User = {
      id: this.currentUserId++,
      username: "admin",
      name: "Admin User",
      email: "<EMAIL>",
      password: adminPassword,
      role: "admin",
      region: "Sydney",
      rating: "5.0",
      totalOrders: 0,
      isActive: true,
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&w=100&h=100&fit=crop&crop=face"
    };
    this.users.set(admin.id, admin);

    // Create sample customer with hashed password
    const customerPassword = await bcrypt.hash("password", 12);
    const customer: User = {
      id: this.currentUserId++,
      username: "rina_customer",
      name: "Rina Customer",
      email: "<EMAIL>",
      password: customerPassword,
      role: "customer",
      region: "Sydney CBD",
      rating: "0",
      totalOrders: 0,
      isActive: true,
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&w=100&h=100&fit=crop&crop=face"
    };
    this.users.set(customer.id, customer);
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    // Hash the password before storing
    const hashedPassword = await bcrypt.hash(insertUser.password, 12);
    
    const user: User = {
      ...insertUser,
      password: hashedPassword,
      id: this.currentUserId++,
      rating: "0",
      totalOrders: 0,
      isActive: true,
      role: insertUser.role || "customer", // Ensure role is always defined
      avatar: insertUser.avatar || null, // Ensure avatar is always defined
      region: insertUser.region || null // Ensure region is always defined
    };
    this.users.set(user.id, user);
    return user;
  }

  async getUsersByRole(role: string): Promise<User[]> {
    return Array.from(this.users.values()).filter(user => user.role === role);
  }

  async updateUserRating(userId: number, rating: number): Promise<void> {
    const user = this.users.get(userId);
    if (user) {
      user.rating = rating.toString();
      this.users.set(userId, user);
    }
  }

  async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  async getDish(id: number): Promise<Dish | undefined> {
    return this.dishes.get(id);
  }

  async getDishes(): Promise<Dish[]> {
    return Array.from(this.dishes.values()).filter(dish => dish.isActive);
  }

  async getDishesByChef(chefId: number): Promise<Dish[]> {
    return Array.from(this.dishes.values()).filter(dish => dish.chefId === chefId && dish.isActive);
  }

  async getDishesWithChef(): Promise<DishWithChef[]> {
    const dishes = await this.getDishes();
    console.log('getDishesWithChef: Found', dishes.length, 'dishes');
    const dishesWithChef: DishWithChef[] = [];

    for (const dish of dishes) {
      console.log('getDishesWithChef: Processing dish', dish.id, 'with chefId', dish.chefId);
      const chef = await this.getUser(dish.chefId);
      console.log('getDishesWithChef: Found chef for dish', dish.id, ':', chef ? chef.name : 'NOT FOUND');
      if (chef) {
        const voteProgress = ((dish.currentVotes || 0) / dish.minOrders) * 100;
        const canStartCooking = (dish.currentVotes || 0) >= dish.minOrders;
        dishesWithChef.push({
          ...dish,
          chef,
          voteProgress,
          canStartCooking
        });
      }
    }

    console.log('getDishesWithChef: Returning', dishesWithChef.length, 'dishes with chef info');
    return dishesWithChef;
  }

  async createDish(insertDish: InsertDish): Promise<Dish> {
    const dish: Dish = {
      ...insertDish,
      id: this.currentDishId++,
      currentVotes: 0,
      isActive: true,
      status: insertDish.status || "draft",
      tags: insertDish.tags || null,
      ingredients: insertDish.ingredients || null,
      allergens: insertDish.allergens || null,
      dietaryInfo: insertDish.dietaryInfo || null,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.dishes.set(dish.id, dish);
    return dish;
  }

  async updateDish(dishId: number, dishData: Partial<InsertDish>): Promise<Dish> {
    const dish = this.dishes.get(dishId);
    if (dish) {
      const updatedDish: Dish = { 
        ...dish, 
        ...dishData,
        updatedAt: new Date() 
      };
      this.dishes.set(dishId, updatedDish);
      return updatedDish;
    }
    throw new Error("Dish not found");
  }

  async deleteDish(dishId: number): Promise<void> {
    const dish = this.dishes.get(dishId);
    if (dish) {
      dish.isActive = false;
      this.dishes.set(dishId, dish);
    }
  }

  async updateDishStatus(dishId: number, status: string): Promise<void> {
    const dish = this.dishes.get(dishId);
    if (dish) {
      dish.status = status;
      dish.updatedAt = new Date();
      this.dishes.set(dishId, dish);
    }
  }

  async updateDishVotes(dishId: number, votes: number): Promise<void> {
    const dish = this.dishes.get(dishId);
    if (dish) {
      dish.currentVotes = votes;
      this.dishes.set(dishId, dish);
    }
  }

  async searchDishes(params: DishSearchParams): Promise<Dish[]> {
    let dishes = Array.from(this.dishes.values()).filter(dish => dish.isActive);

    // Search by query
    if (params.q) {
      const query = params.q.toLowerCase();
      dishes = dishes.filter(dish => 
        dish.name.toLowerCase().includes(query) ||
        dish.description.toLowerCase().includes(query) ||
        dish.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Filter by category
    if (params.category) {
      dishes = dishes.filter(dish => dish.category === params.category);
    }

    // Filter by region
    if (params.region) {
      dishes = dishes.filter(dish => dish.region === params.region);
    }

    // Filter by price range
    if (params.minPrice || params.maxPrice) {
      dishes = dishes.filter(dish => {
        const price = parseFloat(dish.price);
        if (params.minPrice && price < params.minPrice) return false;
        if (params.maxPrice && price > params.maxPrice) return false;
        return true;
      });
    }

    // Filter by cooking time
    if (params.minCookingTime || params.maxCookingTime) {
      dishes = dishes.filter(dish => {
        if (params.minCookingTime && dish.cookingTime < params.minCookingTime) return false;
        if (params.maxCookingTime && dish.cookingTime > params.maxCookingTime) return false;
        return true;
      });
    }

    // Filter by dietary requirements
    if (params.dietary && params.dietary.length > 0) {
      dishes = dishes.filter(dish => {
        if (!dish.dietaryInfo) return false;
        return params.dietary!.some(diet => {
          switch (diet) {
            case 'vegetarian': return dish.dietaryInfo!.vegetarian;
            case 'vegan': return dish.dietaryInfo!.vegan;
            case 'glutenFree': return dish.dietaryInfo!.glutenFree;
            case 'spicy': return dish.dietaryInfo!.spicy;
            default: return false;
          }
        });
      });
    }

    // Filter by tags
    if (params.tags && params.tags.length > 0) {
      dishes = dishes.filter(dish => 
        dish.tags?.some(tag => params.tags!.includes(tag))
      );
    }

    // Filter by status
    if (params.status) {
      dishes = dishes.filter(dish => dish.status === params.status);
    }

    // Filter by chef
    if (params.chefId) {
      dishes = dishes.filter(dish => dish.chefId === params.chefId);
    }

    // Apply pagination
    if (params.limit) {
      const offset = params.offset || 0;
      dishes = dishes.slice(offset, offset + params.limit);
    }

    return dishes;
  }

  async getDishesByCategory(category: string): Promise<Dish[]> {
    return Array.from(this.dishes.values()).filter(dish => 
      dish.category === category && dish.isActive
    );
  }

  async getDishesByRegion(region: string): Promise<Dish[]> {
    return Array.from(this.dishes.values()).filter(dish => 
      dish.region === region && dish.isActive
    );
  }

  async getDishesByTags(tags: string[]): Promise<Dish[]> {
    return Array.from(this.dishes.values()).filter(dish => 
      dish.isActive && dish.tags?.some(tag => tags.includes(tag))
    );
  }

  async getDishCategories(): Promise<DishCategory[]> {
    return Array.from(this.dishCategories.values()).filter(category => category.isActive);
  }

  async createDishCategory(category: InsertDishCategory): Promise<DishCategory> {
    const dishCategory: DishCategory = {
      ...category,
      id: this.currentCategoryId++,
      description: category.description || null,
      icon: category.icon || null,
      color: category.color || null,
      isActive: true
    };
    this.dishCategories.set(dishCategory.id, dishCategory);
    return dishCategory;
  }

  async updateDishCategory(id: number, category: Partial<InsertDishCategory>): Promise<DishCategory> {
    const existingCategory = this.dishCategories.get(id);
    if (existingCategory) {
      const updatedCategory = { ...existingCategory, ...category };
      this.dishCategories.set(id, updatedCategory);
      return updatedCategory;
    }
    throw new Error("Category not found");
  }

  async deleteDishCategory(id: number): Promise<void> {
    const category = this.dishCategories.get(id);
    if (category) {
      category.isActive = false;
      this.dishCategories.set(id, category);
    }
  }

  async createVote(insertVote: InsertVote): Promise<Vote> {
    const vote: Vote = {
      ...insertVote,
      id: this.currentVoteId++,
      customization: insertVote.customization || null,
      createdAt: new Date()
    };
    this.votes.set(vote.id, vote);

    // Update dish vote count
    const dish = this.dishes.get(insertVote.dishId);
    if (dish) {
      const newVoteCount = (dish.currentVotes || 0) + 1;
      dish.currentVotes = newVoteCount;
      this.dishes.set(dish.id, dish);

      // Check if threshold is reached and create orders for all voters
      if (newVoteCount >= dish.minOrders && newVoteCount === dish.minOrders) {
        // Get all votes for this dish
        const dishVotes = await this.getVotesByDish(dish.id);
        
        // Create orders for all voters
        for (const voteRecord of dishVotes) {
          const orderData: InsertOrder = {
            userId: voteRecord.userId,
            dishId: dish.id,
            chefId: dish.chefId,
            status: 'pending',
            totalAmount: dish.price,
            customization: voteRecord.customization as any,
            deliveryAddress: null, // Will be set when user provides address
            estimatedDelivery: null // Will be calculated when cooking starts
          };
          
          await this.createOrder(orderData);
        }
      }
    }

    return vote;
  }

  async getVotesByDish(dishId: number): Promise<Vote[]> {
    return Array.from(this.votes.values()).filter(vote => vote.dishId === dishId);
  }

  async hasUserVoted(userId: number, dishId: number): Promise<boolean> {
    return Array.from(this.votes.values()).some(vote => vote.userId === userId && vote.dishId === dishId);
  }

  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    const order: Order = {
      ...insertOrder,
      id: this.currentOrderId++,
      status: insertOrder.status || "pending",
      customization: insertOrder.customization || null,
      deliveryAddress: insertOrder.deliveryAddress || null,
      estimatedDelivery: insertOrder.estimatedDelivery || null,
      createdAt: new Date()
    };
    this.orders.set(order.id, order);
    return order;
  }

  async getOrders(): Promise<Order[]> {
    return Array.from(this.orders.values());
  }

  async getOrdersByUser(userId: number): Promise<Order[]> {
    return Array.from(this.orders.values()).filter(order => order.userId === userId);
  }

  async getOrdersByChef(chefId: number): Promise<Order[]> {
    return Array.from(this.orders.values()).filter(order => order.chefId === chefId);
  }

  async getOrdersByDish(dishId: number): Promise<Order[]> {
    return Array.from(this.orders.values()).filter(order => order.dishId === dishId);
  }

  async getOrdersWithDetails(): Promise<OrderWithDetails[]> {
    const orders = await this.getOrders();
    const ordersWithDetails: OrderWithDetails[] = [];

    for (const order of orders) {
      const dish = await this.getDish(order.dishId);
      const chef = await this.getUser(order.chefId);
      const customer = await this.getUser(order.userId);

      if (dish && chef && customer) {
        ordersWithDetails.push({
          ...order,
          dish,
          chef,
          customer
        });
      }
    }

    return ordersWithDetails;
  }

  async updateOrderStatus(orderId: number, status: string): Promise<void> {
    const order = this.orders.get(orderId);
    if (order) {
      order.status = status;
      this.orders.set(orderId, order);
    }
  }
}

export const storage = new MemStorage();
