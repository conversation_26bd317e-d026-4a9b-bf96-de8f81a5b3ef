import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider } from '@/context/AuthContext';
import App from '@/App';

// Mock fetch for API calls
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock WebSocket
class MockWebSocket {
  onopen: (() => void) | null = null;
  onmessage: ((event: { data: string }) => void) | null = null;
  onclose: (() => void) | null = null;
  onerror: ((error: any) => void) | null = null;
  readyState = 1; // OPEN
  url: string;

  constructor(url: string) {
    this.url = url;
    setTimeout(() => {
      if (this.onopen) {
        this.onopen();
      }
    }, 10);
  }

  send(data: string) {}
  close() {}
}

global.WebSocket = MockWebSocket as any;

// Helper function to create a proper JWT token
function createMockJWT() {
  const header = { alg: 'HS256', typ: 'JWT' };
  const payload = {
    userId: 1,
    username: 'testuser',
    role: 'customer',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
  };
  
  const encodedHeader = btoa(JSON.stringify(header));
  const encodedPayload = btoa(JSON.stringify(payload));
  const mockSignature = 'mock-signature';
  
  return `${encodedHeader}.${encodedPayload}.${mockSignature}`;
}

// Mock data
const mockUser = {
  id: 1,
  name: "John Doe",
  email: "<EMAIL>",
  username: "testuser",
  password: "password",
  role: "customer",
  region: "North America",
  avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100",
  rating: "4.50",
  totalOrders: 12,
  isActive: true
};

const mockDishes = [
  {
    id: 1,
    name: "Spicy Chicken Curry",
    description: "Authentic Indian curry with tender chicken",
    price: "15.99",
    category: "Main Course",
    cuisine: "Indian",
    spiceLevel: 3,
    isVegetarian: false,
    isVegan: false,
    allergens: ["dairy"],
    preparationTime: 25,
    chefId: 1,
    imageUrl: "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400",
    isAvailable: true,
    rating: "4.5",
    reviewCount: 128
  },
  {
    id: 2,
    name: "Margherita Pizza",
    description: "Classic Italian pizza with fresh mozzarella",
    price: "12.99",
    category: "Main Course",
    cuisine: "Italian",
    spiceLevel: 1,
    isVegetarian: true,
    isVegan: false,
    allergens: ["gluten", "dairy"],
    preparationTime: 20,
    chefId: 2,
    imageUrl: "https://images.unsplash.com/photo-1574071318508-1cdbab80d002?w=400",
    isAvailable: true,
    rating: "4.7",
    reviewCount: 95
  }
];

// Mock console to capture errors
const originalConsoleError = console.error;
const consoleErrors: string[] = [];

beforeEach(() => {
  consoleErrors.length = 0;
  console.error = (...args: any[]) => {
    consoleErrors.push(args.join(' '));
    originalConsoleError(...args);
  };
  
  // Reset localStorage
  (localStorage.getItem as jest.Mock).mockReturnValue(null);
  (localStorage.setItem as jest.Mock).mockClear();
  (localStorage.removeItem as jest.Mock).mockClear();
});

afterEach(() => {
  console.error = originalConsoleError;
  jest.clearAllMocks();
});

describe('CloudKitchen Dashboard Integration E2E', () => {
  describe('Full Application Login Flow', () => {
    it('should complete full login flow and load dashboard without errors', async () => {
      const user = userEvent.setup();
      const mockJWT = createMockJWT();

      // Mock login response
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            user: mockUser,
            token: mockJWT,
            expiresIn: 24 * 60 * 60
          })
        })
        // Mock profile fetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUser
        })
        // Mock dishes fetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockDishes
        });

      render(
        <AuthProvider>
          <App />
        </AuthProvider>
      );

      // Should start with login form
      expect(screen.getByText('Welcome Back')).toBeInTheDocument();

      // Fill in login form
      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByLabelText('Password'), 'testpass');
      
      // Submit login
      await user.click(screen.getByRole('button', { name: 'Sign In' }));

      // Wait for dashboard to load
      await waitFor(() => {
        expect(screen.queryByText('Welcome Back')).not.toBeInTheDocument();
      }, { timeout: 5000 });

      // Verify API calls were made
      expect(mockFetch).toHaveBeenCalledWith('/api/auth/login', expect.any(Object));
      expect(mockFetch).toHaveBeenCalledWith('/api/auth/profile', expect.any(Object));
      expect(mockFetch).toHaveBeenCalledWith('/api/dishes', expect.any(Object));

      // Verify no console errors
      const relevantErrors = consoleErrors.filter(error => 
        !error.includes('Warning:') && 
        !error.includes('act(') &&
        !error.includes('Cannot read properties of undefined')
      );
      expect(relevantErrors).toHaveLength(0);
    });

    it('should display user profile data correctly after login', async () => {
      const user = userEvent.setup();
      const mockJWT = createMockJWT();

      // Set up authenticated state
      (localStorage.getItem as jest.Mock).mockReturnValue(mockJWT);

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUser
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockDishes
        });

      render(
        <AuthProvider>
          <App />
        </AuthProvider>
      );

      // Wait for profile to load
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/auth/profile', {
          headers: {
            'Authorization': `Bearer ${mockJWT}`,
            'Content-Type': 'application/json'
          }
        });
      });

      // Verify user data is accessible (this would depend on your actual UI)
      // The test verifies that the user object structure is correct
      await waitFor(() => {
        expect(consoleErrors.filter(e => e.includes('Cannot read properties of undefined'))).toHaveLength(0);
      });
    });

    it('should load dishes data without errors', async () => {
      const mockJWT = createMockJWT();
      (localStorage.getItem as jest.Mock).mockReturnValue(mockJWT);

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUser
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockDishes
        });

      render(
        <AuthProvider>
          <App />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/dishes', expect.any(Object));
      });

      // Verify dishes data structure is handled correctly
      await waitFor(() => {
        const dishErrors = consoleErrors.filter(error => 
          error.includes('dish') || 
          error.includes('menu') ||
          error.includes('Cannot read properties of undefined')
        );
        expect(dishErrors).toHaveLength(0);
      });
    });
  });

  describe('Authentication State Persistence', () => {
    it('should maintain authentication state after page refresh', async () => {
      const mockJWT = createMockJWT();
      
      // Simulate existing token in localStorage
      (localStorage.getItem as jest.Mock).mockReturnValue(mockJWT);

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUser
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockDishes
        });

      render(
        <AuthProvider>
          <App />
        </AuthProvider>
      );

      // Should automatically fetch profile with stored token
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/auth/profile', {
          headers: {
            'Authorization': `Bearer ${mockJWT}`,
            'Content-Type': 'application/json'
          }
        });
      });

      // Should not show login form
      expect(screen.queryByText('Welcome Back')).not.toBeInTheDocument();
    });

    it('should handle invalid token gracefully', async () => {
      const invalidJWT = 'invalid.token.here';
      (localStorage.getItem as jest.Mock).mockReturnValue(invalidJWT);

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        json: async () => ({ message: 'Invalid token' })
      });

      render(
        <AuthProvider>
          <App />
        </AuthProvider>
      );

      // Should remove invalid token and show login
      await waitFor(() => {
        expect(localStorage.removeItem).toHaveBeenCalledWith('authToken');
        expect(screen.getByText('Welcome Back')).toBeInTheDocument();
      });
    });
  });

  describe('WebSocket Integration', () => {
    it('should establish WebSocket connection in authenticated state', async () => {
      const mockJWT = createMockJWT();
      (localStorage.getItem as jest.Mock).mockReturnValue(mockJWT);

      let wsConnections: MockWebSocket[] = [];
      
      // Override WebSocket to track connections
      const OriginalWebSocket = global.WebSocket;
      global.WebSocket = class extends MockWebSocket {
        constructor(url: string) {
          super(url);
          wsConnections.push(this);
        }
      } as any;

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUser
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockDishes
        });

      render(
        <AuthProvider>
          <App />
        </AuthProvider>
      );

      // Wait for WebSocket connections to be established
      await waitFor(() => {
        expect(wsConnections.length).toBeGreaterThan(0);
      });

      // Verify WebSocket connection doesn't cause errors
      const wsErrors = consoleErrors.filter(error => 
        error.includes('WebSocket') || 
        error.includes('ws:')
      );
      expect(wsErrors).toHaveLength(0);

      // Restore original WebSocket
      global.WebSocket = OriginalWebSocket;
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      const mockJWT = createMockJWT();
      (localStorage.getItem as jest.Mock).mockReturnValue(mockJWT);

      // Mock profile fetch failure
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          json: async () => ({ message: 'Server error' })
        });

      render(
        <AuthProvider>
          <App />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalled();
      });

      // Should handle error gracefully without crashing
      const crashErrors = consoleErrors.filter(error => 
        error.includes('Error:') && 
        !error.includes('Warning:')
      );
      
      // Some errors might be expected for error handling, but no crashes
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
    });

    it('should not have memory leaks or unhandled promises', async () => {
      const mockJWT = createMockJWT();
      (localStorage.getItem as jest.Mock).mockReturnValue(mockJWT);

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUser
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockDishes
        });

      const { unmount } = render(
        <AuthProvider>
          <App />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalled();
      });

      // Unmount component
      act(() => {
        unmount();
      });

      // Wait a bit to see if any cleanup errors occur
      await new Promise(resolve => setTimeout(resolve, 100));

      const memoryLeakErrors = consoleErrors.filter(error => 
        error.includes('memory leak') || 
        error.includes('setState') ||
        error.includes('unmounted component')
      );
      
      expect(memoryLeakErrors).toHaveLength(0);
    });
  });
});
