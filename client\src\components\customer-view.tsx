import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Search, Clock, Star } from "lucide-react";
import DishCard from "./dish-card";
import ChefCard from "./chef-card";
import DeliveryTracking from "./delivery-tracking";
import DishDetailModal from "./dish-detail-modal";
import OrderHistory from "./order-history";
import { useWebSocket } from "@/hooks/use-websocket";
import type { DishWithChef, User } from "@shared/schema";

export default function CustomerView() {
  const [selectedDish, setSelectedDish] = useState<DishWithChef | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  // Real-time WebSocket connection
  useWebSocket();

  // Fallback mock data to ensure content is always displayed
  const mockDishes: DishWithChef[] = [
    {
      id: 1,
      name: "Authentic Hyderabadi Biryani",
      description: "Traditional aromatic rice dish with tender mutton and exotic spices",
      price: 299,
      image: "https://images.unsplash.com/photo-1563379091339-03246963d96c?w=400",
      region: "Hyderabad",
      cookingTime: 45,
      minOrders: 10,
      category: "Main Course",
      currentVotes: 7,
      isActive: true,
      status: "active",
      tags: ["spicy", "traditional"],
      ingredients: ["basmati rice", "mutton", "saffron"],
      allergens: [],
      dietaryInfo: ["non-vegetarian"],
      chefId: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      chef: {
        id: 1,
        name: "Chef Ramesh",
        email: "<EMAIL>",
        username: "chef_ramesh",
        role: "chef",
        region: "Hyderabad",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100",
        rating: "4.8",
        totalOrders: 150,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    }
  ];

  const mockChefs: User[] = [
    {
      id: 1,
      name: "Chef Ramesh",
      email: "<EMAIL>",
      username: "chef_ramesh",
      role: "chef",
      region: "Hyderabad",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100",
      rating: "4.8",
      totalOrders: 150,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // Fetch dishes with chef information
  const { data: fetchedDishes = [], isLoading: dishesLoading, error: dishesError } = useQuery<DishWithChef[]>({
    queryKey: ["/api/dishes"],
    retry: 3,
    retryDelay: 1000,
  });

  // Fetch featured chefs
  const { data: fetchedChefs = [], isLoading: chefsLoading, error: chefsError } = useQuery<User[]>({
    queryKey: ["/api/chefs/featured"],
    retry: 3,
    retryDelay: 1000,
  });

  // Use fetched data if available, otherwise use mock data
  const dishes = fetchedDishes.length > 0 ? fetchedDishes : mockDishes;
  const featuredChefs = fetchedChefs.length > 0 ? fetchedChefs : mockChefs;

  // Debug logging
  console.log('CustomerView Debug:', {
    dishesLoading,
    chefsLoading,
    dishesError: dishesError?.message,
    chefsError: chefsError?.message,
    fetchedDishesCount: fetchedDishes.length,
    fetchedChefsCount: fetchedChefs.length,
    finalDishesCount: dishes.length,
    finalChefsCount: featuredChefs.length
  });

  // Filter dishes based on search
  const filteredDishes = dishes.filter(dish =>
    dish.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    dish.region.toLowerCase().includes(searchQuery.toLowerCase()) ||
    dish.chef?.name?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Show loading state only if both are loading and we have no data
  if ((dishesLoading || chefsLoading) && dishes.length === 0 && featuredChefs.length === 0) {
    return (
      <div className="min-h-screen bg-[var(--primary-dark)] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--accent-green)] mx-auto mb-4"></div>
          <p className="text-[var(--text-secondary)]">Loading delicious dishes...</p>
          <p className="text-xs text-[var(--text-secondary)] mt-2">Connecting to CloudKitchen...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (dishesError || chefsError) {
    return (
      <div className="min-h-screen bg-[var(--primary-dark)] text-white flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Oops! Something went wrong</h2>
          <p className="text-[var(--text-secondary)] mb-4">
            We're having trouble loading the dishes. Please try again.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="btn-primary px-6 py-2 rounded-lg"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[var(--primary-dark)] to-[var(--secondary-dark)] py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-4xl font-bold mb-4">Discover Authentic Regional Flavors</h2>
            <p className="text-[var(--text-secondary)] text-lg mb-8">
              Vote for rare dishes from talented home chefs across India
            </p>
            <div className="relative max-w-md mx-auto">
              <input
                type="text"
                placeholder="Search dishes or regions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-[var(--secondary-dark)] border border-[var(--border-gray)] rounded-lg px-4 py-3 pl-10 focus:ring-2 focus:ring-[var(--accent-green)] focus:border-transparent"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--text-secondary)] w-4 h-4" />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Chefs */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h3 className="text-2xl font-bold mb-8">Featured Chefs</h3>
          {chefsLoading ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-6">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-[var(--secondary-dark)] rounded-xl p-4 text-center">
                    <div className="w-16 h-16 bg-gray-600 rounded-full mx-auto mb-3"></div>
                    <div className="h-4 bg-gray-600 rounded mb-2"></div>
                    <div className="h-3 bg-gray-600 rounded mb-1"></div>
                    <div className="h-3 bg-gray-600 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-6">
              {featuredChefs.map((chef) => (
                <ChefCard key={chef.id} chef={chef} />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Traditional Dishes */}
      <section className="py-12 bg-[var(--secondary-dark)]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h3 className="text-2xl font-bold mb-8">Traditional Dishes</h3>
          {dishesLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-[var(--primary-dark)] rounded-xl overflow-hidden">
                    <div className="w-full h-48 bg-gray-600"></div>
                    <div className="p-4">
                      <div className="h-6 bg-gray-600 rounded mb-2"></div>
                      <div className="h-4 bg-gray-600 rounded mb-3"></div>
                      <div className="h-4 bg-gray-600 rounded mb-3"></div>
                      <div className="h-2 bg-gray-600 rounded mb-3"></div>
                      <div className="h-10 bg-gray-600 rounded"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredDishes.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {filteredDishes.map((dish) => (
                <DishCard
                  key={dish.id}
                  dish={dish}
                  onClick={() => setSelectedDish(dish)}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold mb-4">No dishes found</h3>
              <p className="text-[var(--text-secondary)] mb-6">
                {searchQuery
                  ? `No dishes match "${searchQuery}". Try a different search term.`
                  : "Our chefs are preparing amazing dishes. Check back soon!"
                }
              </p>
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery("")}
                  className="btn-primary px-6 py-2 rounded-lg"
                >
                  Clear Search
                </button>
              )}
            </div>
          )}
        </div>
      </section>

      {/* Active Orders / Tracking */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h3 className="text-2xl font-bold mb-8">Your Active Orders</h3>
          <DeliveryTracking />
        </div>
      </section>

      {/* Order History */}
      <section className="py-12 bg-[var(--secondary-dark)]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <OrderHistory userId={1} /> {/* Mock user ID - in real app this would come from auth context */}
        </div>
      </section>

      {/* Dish Detail Modal */}
      {selectedDish && (
        <DishDetailModal
          dish={selectedDish}
          onClose={() => setSelectedDish(null)}
        />
      )}
    </div>
  );
}
