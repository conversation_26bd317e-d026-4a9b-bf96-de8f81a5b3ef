import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "@/context/AuthContext";
import Home from "@/pages/home";
import AuthPage from "@/pages/auth";
import NotFound from "@/pages/not-found";

function Router() {
  try {
    return (
      <Switch>
        <Route path="/" component={Home} />
        <Route path="/auth" component={AuthPage} />
        <Route component={NotFound} />
      </Switch>
    );
  } catch (error) {
    console.error('Router error:', error);
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">CloudKitchen</h1>
          <p className="text-gray-300 mb-6">Authentic Regional Flavors from Home Chefs</p>
          <p className="text-sm">Discover amazing dishes from talented chefs across India</p>
          <div className="mt-8 text-left max-w-md">
            <p className="mb-2">• Vote for rare dishes and experience true flavors</p>
            <p className="mb-2">• Connect with talented home chefs</p>
            <p className="mb-2">• Explore regional cuisines from across India</p>
          </div>
        </div>
      </div>
    );
  }
}

function App() {
  // Minimal version that always renders content
  // Set document title
  if (typeof document !== 'undefined') {
    document.title = 'CloudKitchen - Authentic Regional Flavors';
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">

      {/* Always visible header */}
      <nav className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <h1 className="text-xl font-bold text-green-400">CloudKitchen</h1>
          <div className="space-x-4">
            <span className="text-gray-300">Home</span>
            <span className="text-gray-300">Login</span>
            <span className="text-gray-300">Dashboard</span>
          </div>
        </div>
      </nav>

      {/* Always visible main content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-4">Authentic Regional Flavors from Home Chefs</h2>
          <p className="text-gray-300 text-lg mb-6">
            Discover amazing dishes from talented chefs across India. Vote for rare dishes and experience true flavors.
          </p>
        </div>

        {/* Sample dishes grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-2">Authentic Hyderabadi Biryani</h3>
            <p className="text-gray-400 mb-3">Traditional aromatic rice dish with tender mutton and exotic spices</p>
            <div className="flex justify-between items-center">
              <span className="text-green-400 font-bold">₹299</span>
              <span className="text-sm text-gray-500">45 min</span>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-2">Manipuri Iromba</h3>
            <p className="text-gray-400 mb-3">Traditional fermented fish curry with fresh vegetables</p>
            <div className="flex justify-between items-center">
              <span className="text-green-400 font-bold">₹199</span>
              <span className="text-sm text-gray-500">30 min</span>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-2">Bihar Litti Chokha</h3>
            <p className="text-gray-400 mb-3">Rustic specialty with sattu filling and traditional accompaniments</p>
            <div className="flex justify-between items-center">
              <span className="text-green-400 font-bold">₹149</span>
              <span className="text-sm text-gray-500">25 min</span>
            </div>
          </div>
        </div>

        {/* Features section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <h4 className="text-lg font-semibold mb-2">🍽️ Authentic Dishes</h4>
            <p className="text-gray-400">Discover rare regional dishes from across India</p>
          </div>
          <div className="text-center">
            <h4 className="text-lg font-semibold mb-2">👨‍🍳 Home Chefs</h4>
            <p className="text-gray-400">Connect with talented home chefs in your area</p>
          </div>
          <div className="text-center">
            <h4 className="text-lg font-semibold mb-2">⭐ Vote & Review</h4>
            <p className="text-gray-400">Vote for dishes and help chefs reach minimum orders</p>
          </div>
        </div>
      </main>

      {/* Try to load the complex app in the background */}
      <div style={{ display: 'none' }}>
        <ComplexApp />
      </div>
    </div>
  );
}

function ComplexApp() {
  try {
    return (
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <TooltipProvider>
            <div className="min-h-screen bg-primary-dark text-white">
              <Toaster />
              <Router />
            </div>
          </TooltipProvider>
        </AuthProvider>
      </QueryClientProvider>
    );
  } catch (error) {
    console.error('Complex app error:', error);
    return null;
  }
}

export default App;
