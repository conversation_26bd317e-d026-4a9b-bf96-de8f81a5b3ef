import request from 'supertest';
import { createServer } from 'http';
import express from 'express';
import { registerRoutes } from '../routes';
import { MemStorage } from '../storage';

describe('Authentication API Endpoints', () => {
  let app: express.Express;
  let server: any;
  let storage: MemStorage;

  beforeAll(async () => {
    app = express();
    app.use(express.json());
    storage = new MemStorage();
    server = await registerRoutes(app, storage);
  });

  afterAll((done) => {
    if (server && typeof server.close === 'function') {
      server.close(done);
    } else {
      done();
    }
  });

  describe('POST /api/auth/register', () => {
    it('should register a new customer successfully', async () => {
      const userData = {
        username: 'testcustomer',
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test Customer',
        role: 'customer',
        region: 'Sydney'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('token');
      expect(response.body.user.username).toBe(userData.username);
      expect(response.body.user.role).toBe('customer');
      expect(response.body.user).not.toHaveProperty('password');
    });

    it('should register a new chef successfully', async () => {
      const userData = {
        username: 'testchef',
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test Chef',
        role: 'chef',
        region: 'Manipur'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('token');
      expect(response.body.user.username).toBe(userData.username);
      expect(response.body.user.role).toBe('chef');
    });

    it('should reject registration with existing username', async () => {
      // First register a user
      const userData = {
        username: 'existinguser',
        email: '<EMAIL>',
        password: 'password123',
        name: 'Existing User',
        role: 'customer',
        region: 'Sydney'
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Then try to register with the same username
      const duplicateUserData = {
        username: 'existinguser',
        email: '<EMAIL>',
        password: 'password123',
        name: 'New User',
        role: 'customer'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(duplicateUserData)
        .expect(400);

      expect(response.body.message).toBe('Username already exists');
    });

    it('should validate required fields', async () => {
      const userData = {
        username: 'test',
        // Missing email, password, name, role
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.message).toBe('Validation failed');
      expect(response.body.errors).toBeDefined();
    });

    it('should validate password length', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: '123', // Too short
        name: 'Test User',
        role: 'customer'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.message).toBe('Validation failed');
    });
  });

  describe('POST /api/auth/login', () => {
    it('should login with valid credentials', async () => {
      // First register a user
      const userData = {
        username: 'logintest',
        email: '<EMAIL>',
        password: 'password123',
        name: 'Login Test',
        role: 'customer',
        region: 'Sydney'
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Then login with the same credentials
      const credentials = {
        username: 'logintest',
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(credentials)
        .expect(200);

      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('token');
      expect(response.body.user.username).toBe('logintest');
      expect(response.body.user.role).toBe('customer');
    });

    it('should reject invalid credentials', async () => {
      // First register a user
      const userData = {
        username: 'invalidtest',
        email: '<EMAIL>',
        password: 'password123',
        name: 'Invalid Test',
        role: 'customer',
        region: 'Sydney'
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Then try to login with wrong password
      const credentials = {
        username: 'invalidtest',
        password: 'wrongpassword'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(credentials)
        .expect(401);

      expect(response.body.message).toBe('Invalid credentials');
    });

    it('should reject non-existent user', async () => {
      const credentials = {
        username: 'nonexistent',
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(credentials)
        .expect(401);

      expect(response.body.message).toBe('Invalid credentials');
    });

    it('should validate required fields', async () => {
      const credentials = {
        username: 'admin'
        // Missing password
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(credentials)
        .expect(400);

      expect(response.body.message).toBe('Validation failed');
    });
  });

  describe('GET /api/auth/profile', () => {
    let authToken: string;

    beforeAll(async () => {
      // Register and login to get token
      const userData = {
        username: 'profiletest',
        email: '<EMAIL>',
        password: 'password123',
        name: 'Profile Test',
        role: 'customer',
        region: 'Sydney'
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({ username: 'profiletest', password: 'password123' });
      
      authToken = loginResponse.body.token;
    });

    it('should get user profile with valid token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('username');
      expect(response.body).toHaveProperty('role');
      expect(response.body).not.toHaveProperty('password');
    });

    it('should reject request without token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .expect(401);

      expect(response.body.message).toBe('Access token required');
    });

    it('should reject request with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid.token.here')
        .expect(403);

      expect(response.body.message).toBe('Invalid or expired token');
    });
  });

  describe('POST /api/auth/logout', () => {
    let authToken: string;

    beforeAll(async () => {
      // Register and login to get token
      const userData = {
        username: 'logouttest',
        email: '<EMAIL>',
        password: 'password123',
        name: 'Logout Test',
        role: 'customer',
        region: 'Sydney'
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({ username: 'logouttest', password: 'password123' });
      
      authToken = loginResponse.body.token;
    });

    it('should logout successfully', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.message).toBe('Logged out successfully');
    });
  });
}); 