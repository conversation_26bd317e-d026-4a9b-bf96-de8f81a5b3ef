node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.git
.gitignore
README.md
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.nyc_output
coverage
.nyc_output
.coverage
.cache
dist
build
.DS_Store
*.log
.vscode
.idea
*.swp
*.swo
*~
.cursor
.cursorignore
.cursorindexingignore
apply-rules.bat
apply-rules.sh
cursor-auto-rules-agile-workflow
web-bundles
samples
mnt
docs
attached_assets 