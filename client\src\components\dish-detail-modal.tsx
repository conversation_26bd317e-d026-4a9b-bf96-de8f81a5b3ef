import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { X, Heart, Vote, Users, Clock, Star, TrendingUp } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import ProgressBar from "./progress-bar";
import { VoteCustomizationForm } from "./vote-customization-form";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import type { DishWithChef, InsertVote } from "@shared/schema";

interface DishDetailModalProps {
  dish: DishWithChef;
  onClose: () => void;
}

// Mock user ID - in real app this would come from auth context
const MOCK_USER_ID = 7;

export default function DishDetailModal({ dish, onClose }: DishDetailModalProps) {
  const [showVoteForm, setShowVoteForm] = useState(false);
  const [voteCount, setVoteCount] = useState(dish.currentVotes || 0);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const voteMutation = useMutation({
    mutationFn: async (voteData: InsertVote) => {
      const response = await apiRequest("POST", "/api/votes", voteData);
      return response.json();
    },
    onSuccess: (data) => {
      // Optimistically update the vote count
      const newVoteCount = voteCount + 1;
      setVoteCount(newVoteCount);
      
      queryClient.invalidateQueries({ queryKey: ["/api/dishes"] });
      toast({
        title: "Vote Submitted! 🎉",
        description: `Your vote for ${dish.name} has been recorded. ${newVoteCount >= dish.minOrders ? 'Threshold reached! Cooking will begin soon!' : `${dish.minOrders - newVoteCount} more votes needed to start cooking.`}`,
      });
      setShowVoteForm(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to submit vote. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleVoteSubmit = (customization: any) => {
    voteMutation.mutate({
      userId: MOCK_USER_ID,
      dishId: dish.id,
      customization: {
        spiceLevel: customization.spiceLevel,
        allergies: customization.allergies,
        specialRequests: customization.specialRequests,
        dietaryPreferences: customization.dietaryPreferences,
        cookingPreferences: customization.cookingPreferences,
      },
    });
  };

  const progress = (voteCount / dish.minOrders) * 100;
  const isReady = voteCount >= dish.minOrders;
  const votesNeeded = Math.max(0, dish.minOrders - voteCount);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardContent className="p-6">
          {/* Header */}
          <div className="flex justify-between items-start mb-6">
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{dish.name}</h2>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span>{dish.chef?.rating || '4.0'}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  <span>{dish.chef?.totalOrders || 0} orders</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{dish.cookingTime} mins</span>
                </div>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Dish Info */}
            <div className="space-y-6">
              {/* Dish Image */}
              <div className="relative">
                <img
                  src={dish.image}
                  alt={dish.name}
                  className="w-full h-64 object-cover rounded-lg"
                />
                <div className="absolute top-4 right-4">
                  <Badge variant={isReady ? "default" : "secondary"} className="text-sm">
                    {isReady ? "Ready to Cook!" : `${votesNeeded} votes needed`}
                  </Badge>
                </div>
              </div>

              {/* Chef Info */}
              <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                <img
                  src={dish.chef?.avatar || `https://ui-avatars.com/api/?name=${dish.chef?.name || 'Chef'}`}
                  alt={dish.chef?.name || 'Chef'}
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div>
                  <h3 className="font-semibold">{dish.chef?.name || 'Unknown Chef'}</h3>
                  <p className="text-sm text-gray-600">{dish.region} Regional Specialist</p>
                  <div className="flex items-center mt-1">
                    <div className="flex text-yellow-400 text-sm">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 fill-current" />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500 ml-1">{dish.chef?.rating || '4.0'}</span>
                  </div>
                </div>
              </div>

              {/* Dish Description */}
              <div>
                <h4 className="font-semibold mb-2">About this dish</h4>
                <p className="text-gray-600 text-sm leading-relaxed">{dish.description}</p>
              </div>

              {/* Dietary Info */}
              {dish.dietaryInfo && (
                <div>
                  <h4 className="font-semibold mb-2">Dietary Information</h4>
                  <div className="flex flex-wrap gap-2">
                    {dish.dietaryInfo.vegetarian && (
                      <Badge variant="outline" className="text-green-600">Vegetarian</Badge>
                    )}
                    {dish.dietaryInfo.vegan && (
                      <Badge variant="outline" className="text-green-600">Vegan</Badge>
                    )}
                    {dish.dietaryInfo.glutenFree && (
                      <Badge variant="outline" className="text-blue-600">Gluten Free</Badge>
                    )}
                    {dish.dietaryInfo.spicy && (
                      <Badge variant="outline" className="text-orange-600">Spicy</Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Tags */}
              {dish.tags && dish.tags.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {dish.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Voting Section */}
            <div className="space-y-6">
              {/* Vote Progress */}
              <div className="bg-gradient-to-br from-green-50 to-blue-50 p-6 rounded-lg border border-green-200">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Vote Progress</h3>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-green-600" />
                    <span className="text-sm font-medium text-green-600">
                      {Math.round(progress)}% Complete
                    </span>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex justify-between text-sm text-gray-600 mb-2">
                    <span>Current Votes: {voteCount}</span>
                    <span>Target: {dish.minOrders}</span>
                  </div>
                  <ProgressBar 
                    progress={progress} 
                    variant="voting" 
                    size="lg"
                    showPercentage={false}
                    animated={true}
                  />
                </div>

                <div className="text-center">
                  {isReady ? (
                    <div className="text-green-600 font-semibold">
                      🎉 Threshold reached! Cooking will begin soon!
                    </div>
                  ) : (
                    <div className="text-gray-600">
                      <div className="font-semibold">{votesNeeded} more votes needed</div>
                      <div className="text-sm">to start cooking this dish</div>
                    </div>
                  )}
                </div>
              </div>

              {/* Price and Action */}
              <div className="text-center space-y-4">
                <div className="text-3xl font-bold text-green-600">
                  ${dish.price}
                </div>
                
                <div className="space-y-3">
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => {/* TODO: Implement save for later */}}
                  >
                    <Heart className="w-4 h-4 mr-2" />
                    Save for Later
                  </Button>
                  
                  {isReady ? (
                    <Button className="w-full bg-yellow-500 hover:bg-yellow-600 text-white">
                      Available Soon!
                    </Button>
                  ) : (
                    <Button 
                      className="w-full"
                      onClick={() => setShowVoteForm(true)}
                      disabled={voteMutation.isPending}
                    >
                      <Vote className="w-4 h-4 mr-2" />
                      {voteMutation.isPending ? "Voting..." : `Vote $${dish.price}`}
                    </Button>
                  )}
                </div>

                <div className="text-xs text-gray-500">
                  Your vote helps bring this dish to life! 🍽️
                </div>
              </div>

              {/* Vote Form Modal */}
              {showVoteForm && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                  <div className="w-full max-w-2xl">
                    <VoteCustomizationForm
                      dishName={dish.name}
                      price={dish.price}
                      onSubmit={handleVoteSubmit}
                      onCancel={() => setShowVoteForm(false)}
                      isLoading={voteMutation.isPending}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
