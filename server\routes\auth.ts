import { Router } from 'express';
import { z } from 'zod';
import { storage as defaultStorage } from '../storage';
import { generateToken } from '../utils/jwt';
import { authenticateToken, requireRole } from '../middleware/auth';
import type { IStorage } from '../storage';

export default function createAuthRoutes(storage: IStorage = defaultStorage) {
  const router = Router();

  // Validation schemas
  const loginSchema = z.object({
    username: z.string().min(1, 'Username is required'),
    password: z.string().min(1, 'Password is required'),
  });

  const registerSchema = z.object({
    username: z.string().min(3, 'Username must be at least 3 characters'),
    email: z.string().email('Invalid email address'),
    password: z.string().min(6, 'Password must be at least 6 characters'),
    name: z.string().min(1, 'Name is required'),
    role: z.enum(['customer', 'chef']),
    region: z.string().optional(),
  });

  const updateProfileSchema = z.object({
    name: z.string().min(1, 'Name is required').optional(),
    email: z.string().email('Invalid email address').optional(),
    region: z.string().optional(),
    avatar: z.string().url('Invalid avatar URL').optional(),
  });

  // POST /api/auth/register
  router.post('/register', async (req, res) => {
    try {
      const userData = registerSchema.parse(req.body);

      // Check if username already exists
      const existingUser = await storage.getUserByUsername(userData.username);
      if (existingUser) {
        return res.status(400).json({ message: 'Username already exists' });
      }

      // Create new user
      const user = await storage.createUser(userData);

      // Generate JWT token
      const token = generateToken(user);

      // Remove password from response
      const { password, ...userWithoutPassword } = user;

      res.status(201).json({
        user: userWithoutPassword,
        token,
        expiresIn: 24 * 60 * 60, // 24 hours in seconds
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: 'Validation failed', 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: 'Failed to register user' });
    }
  });

  // POST /api/auth/login
  router.post('/login', async (req, res) => {
    try {
      const { username, password } = loginSchema.parse(req.body);

      // Find user by username
      const user = await storage.getUserByUsername(username);
      if (!user) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Verify password
      const isValidPassword = await storage.verifyPassword(password, user.password);
      if (!isValidPassword) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(401).json({ message: 'Account is deactivated' });
      }

      // Generate JWT token
      const token = generateToken(user);

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user;

      res.json({
        user: userWithoutPassword,
        token,
        expiresIn: 24 * 60 * 60, // 24 hours in seconds
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: 'Validation failed', 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: 'Failed to login' });
    }
  });

  // POST /api/auth/logout
  router.post('/logout', authenticateToken, (req, res) => {
    // In a stateless JWT system, logout is handled client-side
    // by removing the token. The server doesn't need to do anything.
    res.json({ message: 'Logged out successfully' });
  });

  // GET /api/auth/profile
  router.get('/profile', authenticateToken, async (req, res) => {
    try {
      const user = await storage.getUser(req.user!.userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Remove password from response
      const { password, ...userWithoutPassword } = user;
      res.json(userWithoutPassword);
    } catch (error) {
      res.status(500).json({ message: 'Failed to fetch profile' });
    }
  });

  // PUT /api/auth/profile
  router.put('/profile', authenticateToken, async (req, res) => {
    try {
      const updateData = updateProfileSchema.parse(req.body);
      
      // Get current user
      const currentUser = await storage.getUser(req.user!.userId);
      if (!currentUser) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Update user data (in a real app, you'd have an updateUser method)
      // For now, we'll just return the current user with updated fields
      const updatedUser = {
        ...currentUser,
        ...updateData,
      };

      // Remove password from response
      const { password, ...userWithoutPassword } = updatedUser;
      res.json(userWithoutPassword);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: 'Validation failed', 
          errors: error.errors 
        });
      }
      res.status(500).json({ message: 'Failed to update profile' });
    }
  });

  // GET /api/auth/me (alias for profile)
  router.get('/me', authenticateToken, async (req, res) => {
    try {
      const user = await storage.getUser(req.user!.userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Remove password from response
      const { password, ...userWithoutPassword } = user;
      res.json(userWithoutPassword);
    } catch (error) {
      res.status(500).json({ message: 'Failed to fetch user data' });
    }
  });

  return router;
} 