import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Plus, Edit, Trash2, Eye, MoreHorizontal } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu';
import { DishForm } from '../dishes/DishForm';
import { DishSearch } from '../dishes/DishSearch';
import { type Dish, type InsertDish, type DishCategory, type DishFilter } from '@shared/schema';

interface DishManagementProps {
  chefId: number;
}

export const DishManagement: React.FC<DishManagementProps> = ({ chefId }) => {
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [categories, setCategories] = useState<DishCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [editingDish, setEditingDish] = useState<Dish | null>(null);
  const [selectedTab, setSelectedTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchFilters, setSearchFilters] = useState<DishFilter>({});

  const regions = [
    'Manipur', 'Nagaland', 'Arunachal Pradesh', 'Bihar', 'Assam', 
    'Meghalaya', 'Mizoram', 'Tripura', 'Sikkim', 'Sydney CBD'
  ];

  useEffect(() => {
    loadDishes();
    loadCategories();
  }, [chefId]);

  const loadDishes = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/dishes?chefId=${chefId}`);
      if (response.ok) {
        const data = await response.json();
        setDishes(data);
      }
    } catch (error) {
      console.error('Error loading dishes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const response = await fetch('/api/dishes/categories/all');
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const handleCreateDish = async (dishData: InsertDish) => {
    try {
      const response = await fetch('/api/dishes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(dishData),
      });

      if (response.ok) {
        const newDish = await response.json();
        setDishes(prev => [...prev, newDish]);
        setIsCreating(false);
      } else {
        throw new Error('Failed to create dish');
      }
    } catch (error) {
      console.error('Error creating dish:', error);
    }
  };

  const handleUpdateDish = async (dishData: InsertDish) => {
    if (!editingDish) return;

    try {
      const response = await fetch(`/api/dishes/${editingDish.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(dishData),
      });

      if (response.ok) {
        const updatedDish = await response.json();
        setDishes(prev => prev.map(dish => dish.id === editingDish.id ? updatedDish : dish));
        setEditingDish(null);
      } else {
        throw new Error('Failed to update dish');
      }
    } catch (error) {
      console.error('Error updating dish:', error);
    }
  };

  const handleDeleteDish = async (dishId: number) => {
    if (!confirm('Are you sure you want to delete this dish?')) return;

    try {
      const response = await fetch(`/api/dishes/${dishId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        setDishes(prev => prev.filter(dish => dish.id !== dishId));
      } else {
        throw new Error('Failed to delete dish');
      }
    } catch (error) {
      console.error('Error deleting dish:', error);
    }
  };

  const handleStatusChange = async (dishId: number, status: string) => {
    try {
      const response = await fetch(`/api/dishes/${dishId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        setDishes(prev => prev.map(dish => 
          dish.id === dishId ? { ...dish, status } : dish
        ));
      } else {
        throw new Error('Failed to update dish status');
      }
    } catch (error) {
      console.error('Error updating dish status:', error);
    }
  };

  const handleSearch = (query: string, filters: DishFilter) => {
    setSearchQuery(query);
    setSearchFilters(filters);
  };

  const getFilteredDishes = () => {
    let filtered = dishes;

    if (selectedTab !== 'all') {
      filtered = filtered.filter(dish => dish.status === selectedTab);
    }

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(dish => 
        dish.name.toLowerCase().includes(query) ||
        dish.description.toLowerCase().includes(query) ||
        dish.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    if (searchFilters.category) {
      filtered = filtered.filter(dish => dish.category === searchFilters.category);
    }
    if (searchFilters.region) {
      filtered = filtered.filter(dish => dish.region === searchFilters.region);
    }
    if (searchFilters.status) {
      filtered = filtered.filter(dish => dish.status === searchFilters.status);
    }

    return filtered;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Active';
      case 'draft': return 'Draft';
      case 'inactive': return 'Inactive';
      default: return status;
    }
  };

  const filteredDishes = getFilteredDishes();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading dishes...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Dish Management</h1>
          <p className="text-gray-600">Manage your culinary offerings</p>
        </div>
        <Dialog open={isCreating} onOpenChange={setIsCreating}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create New Dish
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Dish</DialogTitle>
            </DialogHeader>
            <DishForm
              categories={categories}
              onSubmit={handleCreateDish}
              onCancel={() => setIsCreating(false)}
              isLoading={false}
            />
          </DialogContent>
        </Dialog>
      </div>

      <DishSearch
        onSearch={handleSearch}
        categories={categories}
        regions={regions}
        isLoading={false}
      />

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{dishes.length}</div>
            <div className="text-sm text-gray-600">Total Dishes</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {dishes.filter(d => d.status === 'active').length}
            </div>
            <div className="text-sm text-gray-600">Active Dishes</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {dishes.filter(d => d.status === 'draft').length}
            </div>
            <div className="text-sm text-gray-600">Draft Dishes</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {dishes.reduce((sum, dish) => sum + (dish.currentVotes || 0), 0)}
            </div>
            <div className="text-sm text-gray-600">Total Votes</div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList>
          <TabsTrigger value="all">All Dishes ({dishes.length})</TabsTrigger>
          <TabsTrigger value="active">Active ({dishes.filter(d => d.status === 'active').length})</TabsTrigger>
          <TabsTrigger value="draft">Draft ({dishes.filter(d => d.status === 'draft').length})</TabsTrigger>
          <TabsTrigger value="inactive">Inactive ({dishes.filter(d => d.status === 'inactive').length})</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedTab} className="mt-6">
          {filteredDishes.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg">No dishes found</div>
              <p className="text-gray-400">Create your first dish to get started</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredDishes.map((dish) => (
                <Card key={dish.id} className="relative">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <CardTitle className="text-lg">{dish.name}</CardTitle>
                        <p className="text-sm text-gray-600 line-clamp-2">{dish.description}</p>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => setEditingDish(dish)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleStatusChange(dish.id, 'active')}>
                            <Eye className="h-4 w-4 mr-2" />
                            Activate
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleStatusChange(dish.id, 'inactive')}>
                            <Eye className="h-4 w-4 mr-2" />
                            Deactivate
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDeleteDish(dish.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-bold text-green-600">${dish.price}</span>
                        <Badge className={getStatusColor(dish.status)}>
                          {getStatusLabel(dish.status)}
                        </Badge>
                      </div>
                      
                      <div className="flex justify-between text-sm text-gray-600">
                        <span>{dish.cookingTime} mins</span>
                        <span>{(dish.currentVotes || 0)}/{dish.minOrders} votes</span>
                      </div>

                      <div className="flex justify-between text-sm text-gray-600">
                        <span>{dish.category}</span>
                        <span>{dish.region}</span>
                      </div>

                      {dish.tags && dish.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {dish.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {dish.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{dish.tags.length - 3} more
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {editingDish && (
        <Dialog open={!!editingDish} onOpenChange={() => setEditingDish(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Dish</DialogTitle>
            </DialogHeader>
            <DishForm
              dish={editingDish}
              categories={categories}
              onSubmit={handleUpdateDish}
              onCancel={() => setEditingDish(null)}
              isLoading={false}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}; 