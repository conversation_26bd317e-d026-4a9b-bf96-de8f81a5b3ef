import { useState, useEffect } from "react";
import { Bell, User, LogOut, Settings } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useLocation } from "wouter";
import { usePageTitle } from "@/hooks/use-page-title";
import CustomerView from "@/components/customer-view";
import ChefView from "@/components/chef-view";
import AdminView from "@/components/admin-view";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

type ViewType = "customer" | "chef" | "admin";

export default function Home() {
  const { user, isAuthenticated, logout, loading } = useAuth();
  const [, setLocation] = useLocation();
  const [activeView, setActiveView] = useState<ViewType>("customer");

  // Set page title based on active view and user
  const getPageTitle = () => {
    const viewTitles = {
      customer: "Discover Dishes",
      chef: "Chef Dashboard",
      admin: "Admin Dashboard"
    };
    const baseTitle = viewTitles[activeView] || "Dashboard";
    return `${baseTitle} - CloudKitchen`;
  };

  usePageTitle(getPageTitle());

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-[var(--primary-dark)] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--accent-green)] mx-auto mb-4"></div>
          <p className="text-[var(--text-secondary)]">Loading CloudKitchen...</p>
        </div>
      </div>
    );
  }

  // Redirect to auth page if not authenticated
  if (!isAuthenticated) {
    setLocation('/auth');
    return null;
  }

  // Set active view based on user role
  const userRole = user?.role as ViewType;
  if (activeView !== userRole && userRole) {
    setActiveView(userRole);
  }

  const handleLogout = async () => {
    await logout();
    setLocation('/auth');
  };

  const tabs = [
    { id: "customer", label: "Customer", icon: "🍽️", roles: ["customer"] as const },
    { id: "chef", label: "Chef", icon: "👨‍🍳", roles: ["chef"] as const },
    { id: "admin", label: "Admin", icon: "⚙️", roles: ["admin"] as const }
  ] as const;

  const availableTabs = tabs.filter(tab => {
    if (user?.role === 'admin') return true;
    return tab.id === user?.role;
  });

  return (
    <div className="min-h-screen bg-[var(--primary-dark)] text-white">
      {/* Navigation Header */}
      <nav className="bg-[var(--secondary-dark)] border-b border-[var(--border-gray)] sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-bold text-[var(--accent-green)]">CloudKitchen</h1>
              <div className="hidden sm:flex space-x-6">
                {availableTabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveView(tab.id as ViewType)}
                    className={`nav-tab ${activeView === tab.id ? 'active' : ''}`}
                  >
                    <span className="mr-2">{tab.icon}</span>
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 rounded-full hover:bg-gray-700 transition-colors">
                <Bell className="w-5 h-5 text-[var(--text-secondary)]" />
              </button>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user?.avatar || undefined} alt={user?.name} />
                      <AvatarFallback>
                        {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{user?.name}</p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user?.email}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground capitalize">
                        {user?.role}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <div className="sm:hidden bg-[var(--secondary-dark)] border-b border-[var(--border-gray)]">
        <div className="flex justify-around py-2">
          {availableTabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveView(tab.id as ViewType)}
              className={`nav-tab flex-1 text-center ${activeView === tab.id ? 'active' : ''}`}
            >
              <span className="block">{tab.icon}</span>
              <span className="text-xs">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content Views */}
      <main className="relative">
        {activeView === "customer" && <CustomerView />}
        {activeView === "chef" && <ChefView />}
        {activeView === "admin" && <AdminView />}
      </main>
    </div>
  );
}
