import { Clock, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { RealTimeVoteCounter } from "./real-time-vote-counter";
import type { DishWithChef } from "@shared/schema";

interface DishCardProps {
  dish: DishWithChef;
  onClick?: () => void;
}

export default function DishCard({ dish, onClick }: DishCardProps) {
  const isReady = (dish.currentVotes || 0) >= dish.minOrders;

  return (
    <Card 
      className="dish-card bg-[var(--primary-dark)] border-[var(--border-gray)] overflow-hidden cursor-pointer transition-all hover:shadow-lg hover:scale-[1.02] duration-200"
      onClick={onClick}
    >
      <div className="relative">
        <img
          src={dish.image}
          alt={dish.name}
          className="w-full h-48 object-cover"
        />
        <div className="absolute top-2 right-2 bg-black bg-opacity-70 px-2 py-1 rounded text-xs">
          <div className="flex items-center text-yellow-400">
            <Star className="w-3 h-3 mr-1 fill-current" />
            {dish.chef?.rating || '4.0'}
          </div>
        </div>
        <div className="absolute top-2 left-2">
          <div className="bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium text-gray-800">
            ${dish.price}
          </div>
        </div>
      </div>
      
      <CardContent className="p-4">
        <h4 className="font-semibold mb-2 line-clamp-1">{dish.name}</h4>
        <p className="text-[var(--text-secondary)] text-sm mb-3 line-clamp-2">
          {dish.description}
        </p>
        
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center text-sm text-[var(--text-secondary)]">
            <Clock className="w-4 h-4 mr-1" />
            <span>{dish.cookingTime} min</span>
          </div>
          <div className="text-xs text-[var(--text-secondary)]">
            by {dish.chef?.name || 'Unknown Chef'}
          </div>
        </div>

        {/* Real-time Vote Counter */}
        <div className="mb-3">
          <RealTimeVoteCounter
            currentVotes={dish.currentVotes || 0}
            minOrders={dish.minOrders}
            showDetails={false}
            animated={true}
            className="border-0 shadow-none bg-transparent"
          />
        </div>

        <Button 
          className={`w-full font-medium transition-colors ${
            isReady 
              ? 'bg-yellow-500 hover:bg-yellow-600 text-white' 
              : 'btn-primary'
          }`}
          onClick={(e) => {
            e.stopPropagation();
            // Handle vote action
          }}
        >
          {isReady ? 'Ready to Cook!' : `Vote $${dish.price}`}
        </Button>

        <div className="mt-2 text-xs text-[var(--text-secondary)] text-center">
          {dish.region} • {dish.chef.totalOrders} orders
        </div>
      </CardContent>
    </Card>
  );
}
