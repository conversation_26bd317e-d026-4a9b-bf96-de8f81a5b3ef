import React, { useState, useEffect } from 'react';
import { Badge } from './ui/badge';
import { Card, CardContent } from './ui/card';
import { TrendingUp, Users, Target, CheckCircle } from 'lucide-react';
import ProgressBar from './progress-bar';

interface RealTimeVoteCounterProps {
  currentVotes: number;
  minOrders: number;
  className?: string;
  showDetails?: boolean;
  animated?: boolean;
}

export const RealTimeVoteCounter: React.FC<RealTimeVoteCounterProps> = ({
  currentVotes,
  minOrders,
  className = '',
  showDetails = true,
  animated = true
}) => {
  const [displayVotes, setDisplayVotes] = useState(0);
  const [isThresholdReached, setIsThresholdReached] = useState(false);

  const progress = (currentVotes / minOrders) * 100;
  const votesNeeded = Math.max(0, minOrders - currentVotes);
  const isComplete = currentVotes >= minOrders;

  // Animate vote count
  useEffect(() => {
    if (animated) {
      const timer = setTimeout(() => {
        setDisplayVotes(currentVotes);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setDisplayVotes(currentVotes);
    }
  }, [currentVotes, animated]);

  // Check if threshold was just reached
  useEffect(() => {
    if (isComplete && !isThresholdReached) {
      setIsThresholdReached(true);
      // Reset after animation
      setTimeout(() => setIsThresholdReached(false), 3000);
    }
  }, [isComplete, isThresholdReached]);

  return (
    <Card className={`vote-counter ${className} ${isThresholdReached ? 'ring-2 ring-green-500 ring-opacity-50' : ''}`}>
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Vote Count Display */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-gray-600">Current Votes</span>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-600">
                {displayVotes}
              </div>
              <div className="text-xs text-gray-500">of {minOrders}</div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>Progress</span>
              <span className="font-medium">{Math.round(progress)}%</span>
            </div>
            <ProgressBar 
              progress={progress} 
              variant={isComplete ? "success" : "voting"}
              size="md"
              animated={animated}
            />
          </div>

          {/* Status Display */}
          <div className="text-center">
            {isComplete ? (
              <div className="flex items-center justify-center gap-2 text-green-600 font-semibold">
                <CheckCircle className="w-5 h-5" />
                <span>Threshold Reached! 🎉</span>
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2 text-gray-600">
                <Target className="w-4 h-4" />
                <span className="text-sm">
                  {votesNeeded} more vote{votesNeeded !== 1 ? 's' : ''} needed
                </span>
              </div>
            )}
          </div>

          {/* Additional Details */}
          {showDetails && (
            <div className="pt-3 border-t border-gray-100">
              <div className="grid grid-cols-2 gap-4 text-xs text-gray-500">
                <div className="text-center">
                  <div className="font-medium text-gray-700">Votes Needed</div>
                  <div className="text-lg font-bold text-orange-600">{votesNeeded}</div>
                </div>
                <div className="text-center">
                  <div className="font-medium text-gray-700">Progress</div>
                  <div className="text-lg font-bold text-blue-600">{Math.round(progress)}%</div>
                </div>
              </div>
            </div>
          )}

          {/* Threshold Reached Animation */}
          {isThresholdReached && (
            <div className="absolute inset-0 bg-green-50 border-2 border-green-500 rounded-lg flex items-center justify-center animate-pulse">
              <div className="text-center">
                <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <div className="text-green-600 font-bold">Threshold Reached!</div>
                <div className="text-green-500 text-sm">Cooking will begin soon</div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}; 