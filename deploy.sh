#!/bin/bash

# CloudKitchen Deployment Script
# This script helps deploy the application to various platforms

set -e

echo "🚀 CloudKitchen Deployment Script"
echo "=================================="

# Check if required environment variables are set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ Error: DATABASE_URL environment variable is required"
    echo "Please set it to your PostgreSQL database URL"
    exit 1
fi

if [ -z "$JWT_SECRET" ]; then
    echo "❌ Error: JWT_SECRET environment variable is required"
    echo "Please set it to a secure random string"
    exit 1
fi

if [ -z "$SESSION_SECRET" ]; then
    echo "❌ Error: SESSION_SECRET environment variable is required"
    echo "Please set it to a secure random string"
    exit 1
fi

echo "✅ Environment variables validated"

# Build the application
echo "🔨 Building application..."
npm run build

echo "✅ Build completed"

# Function to deploy to Railway
deploy_railway() {
    echo "🚂 Deploying to Railway..."
    if command -v railway &> /dev/null; then
        railway up
    else
        echo "❌ Railway CLI not found. Please install it first:"
        echo "npm install -g @railway/cli"
        exit 1
    fi
}

# Function to deploy to Render
deploy_render() {
    echo "🎨 Deploying to Render..."
    echo "Please deploy manually using the render.yaml file:"
    echo "1. Go to https://render.com"
    echo "2. Create a new Web Service"
    echo "3. Connect your GitHub repository"
    echo "4. Render will automatically detect the render.yaml file"
}

# Function to deploy to Vercel
deploy_vercel() {
    echo "⚡ Deploying to Vercel..."
    if command -v vercel &> /dev/null; then
        vercel --prod
    else
        echo "❌ Vercel CLI not found. Please install it first:"
        echo "npm install -g vercel"
        exit 1
    fi
}

# Function to deploy using Docker
deploy_docker() {
    echo "🐳 Building and running with Docker..."
    docker build -t cloudkitchen .
    docker run -d \
        --name cloudkitchen \
        -p 5000:5000 \
        -e DATABASE_URL="$DATABASE_URL" \
        -e JWT_SECRET="$JWT_SECRET" \
        -e SESSION_SECRET="$SESSION_SECRET" \
        -e NODE_ENV=production \
        cloudkitchen
    echo "✅ Docker container started on port 5000"
}

# Main deployment logic
case "${1:-}" in
    "railway")
        deploy_railway
        ;;
    "render")
        deploy_render
        ;;
    "vercel")
        deploy_vercel
        ;;
    "docker")
        deploy_docker
        ;;
    "local")
        echo "🏠 Starting local development server..."
        npm run dev
        ;;
    *)
        echo "Usage: $0 {railway|render|vercel|docker|local}"
        echo ""
        echo "Deployment options:"
        echo "  railway  - Deploy to Railway"
        echo "  render   - Deploy to Render"
        echo "  vercel   - Deploy to Vercel"
        echo "  docker   - Run with Docker locally"
        echo "  local    - Start development server"
        echo ""
        echo "Required environment variables:"
        echo "  DATABASE_URL    - PostgreSQL database URL"
        echo "  JWT_SECRET      - Secret for JWT tokens"
        echo "  SESSION_SECRET  - Secret for sessions"
        exit 1
        ;;
esac

echo "🎉 Deployment completed successfully!" 