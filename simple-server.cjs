const express = require('express');
const path = require('path');
const fs = require('fs');
const { createServer } = require('http');
const { WebSocketServer } = require('ws');

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });
const PORT = process.env.PORT || 5000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  const originalSend = res.send;
  
  res.send = function(data) {
    const duration = Date.now() - start;
    if (req.path.startsWith('/api')) {
      console.log(`${req.method} ${req.path} ${res.statusCode} in ${duration}ms`);
    }
    return originalSend.call(this, data);
  };
  
  next();
});

// Mock data for demonstration
const mockDishes = [
  {
    id: 1,
    name: "Margherita Pizza",
    description: "Classic pizza with tomato sauce, mozzarella, and fresh basil",
    price: "15.99",
    category: "Italian",
    region: "Europe",
    status: "available",
    chefId: 1,
    cookingTime: 25,
    minOrders: 1,
    currentVotes: 5,
    image: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400",
    tags: ["vegetarian", "popular"],
    ingredients: ["tomato sauce", "mozzarella", "basil", "olive oil"],
    allergens: ["dairy", "gluten"],
    dietaryInfo: {
      vegetarian: true,
      vegan: false,
      glutenFree: false,
      spicy: false
    },
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    name: "Chicken Tikka Masala",
    description: "Tender chicken in a creamy tomato-based curry sauce",
    price: "18.99",
    category: "Indian",
    region: "Asia",
    status: "available",
    chefId: 2,
    cookingTime: 35,
    minOrders: 2,
    currentVotes: 8,
    image: "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400",
    tags: ["spicy", "popular", "protein"],
    ingredients: ["chicken", "tomatoes", "cream", "spices"],
    allergens: ["dairy"],
    dietaryInfo: {
      vegetarian: false,
      vegan: false,
      glutenFree: true,
      spicy: true
    },
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

const mockUsers = [
  {
    id: 1,
    name: "John Doe",
    email: "<EMAIL>",
    username: "johndoe",
    password: "password", // In real app, this would be hashed
    role: "customer",
    region: "North America",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100",
    rating: "4.50", // Decimal as string to match schema
    totalOrders: 12,
    isActive: true
  }
];

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: 'CloudKitchen API is running',
    timestamp: new Date().toISOString(),
    database: 'connected'
  });
});

app.get('/api/dishes', (req, res) => {
  res.json(mockDishes);
});

app.get('/api/dishes/:id', (req, res) => {
  const dish = mockDishes.find(d => d.id === parseInt(req.params.id));
  if (!dish) {
    return res.status(404).json({ error: 'Dish not found' });
  }
  res.json(dish);
});

app.post('/api/dishes', (req, res) => {
  const newDish = {
    id: mockDishes.length + 1,
    ...req.body,
    currentVotes: 0,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  mockDishes.push(newDish);
  res.status(201).json(newDish);
});

app.get('/api/users/me', (req, res) => {
  res.json(mockUsers[0]);
});

app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;

  if (username && password) {
    // Create a mock JWT token with proper structure
    const header = { alg: 'HS256', typ: 'JWT' };
    const payload = {
      userId: mockUsers[0].id,
      username: mockUsers[0].username,
      role: mockUsers[0].role,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    };

    // Create a mock JWT (base64 encoded header.payload.signature)
    const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
    const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64url');
    const mockSignature = 'mock-signature';
    const mockJWT = `${encodedHeader}.${encodedPayload}.${mockSignature}`;

    res.json({
      user: mockUsers[0],
      token: mockJWT,
      expiresIn: 24 * 60 * 60
    });
  } else {
    res.status(400).json({ error: 'Username and password required' });
  }
});

app.post('/api/auth/register', (req, res) => {
  const { name, email, username, password } = req.body;

  if (name && email && username && password) {
    const newUser = {
      id: mockUsers.length + 1,
      name,
      email,
      username,
      role: 'customer',
      region: 'Unknown',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
      rating: '0',
      totalOrders: 0,
      isActive: true
    };
    mockUsers.push(newUser);

    res.status(201).json({
      user: newUser,
      token: 'mock-jwt-token',
      expiresIn: 24 * 60 * 60,
      message: 'Registration successful'
    });
  } else {
    res.status(400).json({ error: 'All fields are required' });
  }
});

app.get('/api/auth/profile', (req, res) => {
  const authHeader = req.headers['authorization'];
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  const token = authHeader.split(' ')[1];

  try {
    // For mock purposes, decode the JWT-like token we created
    const parts = token.split('.');
    if (parts.length === 3) {
      const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
      const currentTime = Math.floor(Date.now() / 1000);

      if (payload.exp > currentTime) {
        res.json(mockUsers[0]);
      } else {
        res.status(403).json({ message: 'Token expired' });
      }
    } else {
      res.status(403).json({ message: 'Invalid token format' });
    }
  } catch (error) {
    res.status(403).json({ message: 'Invalid token' });
  }
});

app.put('/api/auth/profile', (req, res) => {
  // Mock authentication - in real app, verify JWT token
  const authHeader = req.headers['authorization'];
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  const { name, email, region, avatar } = req.body;
  const updatedUser = {
    ...mockUsers[0],
    ...(name && { name }),
    ...(email && { email }),
    ...(region && { region }),
    ...(avatar && { avatar })
  };

  mockUsers[0] = updatedUser;
  res.json(updatedUser);
});

app.post('/api/auth/logout', (req, res) => {
  // Mock logout - in real app, handle token invalidation
  res.json({ message: 'Logged out successfully' });
});

// Serve static files from the built frontend
app.use(express.static(path.join(__dirname, 'dist/public')));

// Catch-all handler: send back React's index.html file for any non-API routes
app.get('*', (req, res) => {
  const indexPath = path.join(__dirname, 'dist/public/index.html');
  if (fs.existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    res.status(404).json({ error: 'Frontend not built. Run npm run build first.' });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({ 
    error: 'Internal server error',
    message: err.message 
  });
});

// WebSocket connection handling
wss.on('connection', (ws) => {
  console.log('🔌 WebSocket client connected');

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'connection',
    message: 'Connected to CloudKitchen WebSocket'
  }));

  // Handle messages from client
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      console.log('📨 WebSocket message received:', data);

      // Echo back for now - in real app, handle different message types
      ws.send(JSON.stringify({
        type: 'echo',
        data: data
      }));
    } catch (error) {
      console.error('❌ WebSocket message error:', error);
    }
  });

  ws.on('close', () => {
    console.log('🔌 WebSocket client disconnected');
  });

  ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error);
  });
});

// Start server
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 CloudKitchen server running on port ${PORT}`);
  console.log(`📱 Frontend: http://localhost:${PORT}`);
  console.log(`🔗 API: http://localhost:${PORT}/api/health`);
  console.log(`🔌 WebSocket: ws://localhost:${PORT}/ws`);
  console.log(`💾 Database: PostgreSQL (${process.env.DATABASE_URL || 'Not configured'})`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});
