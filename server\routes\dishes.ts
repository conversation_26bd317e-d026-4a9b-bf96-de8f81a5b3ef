import express from 'express';
import { storage } from '../storage';
import { authenticateToken, requireRole } from '../middleware/auth';
import { type DishSearchParams, type InsertDish } from '../../shared/schema';

const router = express.Router();

// Get all dishes with chef information and optional filtering
router.get('/', async (req, res) => {
  try {
    // If there are search parameters, use search function
    if (Object.keys(req.query).length > 0) {
      const searchParams: DishSearchParams = {
        q: req.query.q as string,
        category: req.query.category as string,
        region: req.query.region as string,
        minPrice: req.query.minPrice ? parseFloat(req.query.minPrice as string) : undefined,
        maxPrice: req.query.maxPrice ? parseFloat(req.query.maxPrice as string) : undefined,
        minCookingTime: req.query.minCookingTime ? parseInt(req.query.minCookingTime as string) : undefined,
        maxCookingTime: req.query.maxCookingTime ? parseInt(req.query.maxCookingTime as string) : undefined,
        dietary: req.query.dietary ? (req.query.dietary as string).split(',') : undefined,
        tags: req.query.tags ? (req.query.tags as string).split(',') : undefined,
        status: req.query.status as string,
        chefId: req.query.chefId ? parseInt(req.query.chefId as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        offset: req.query.offset ? parseInt(req.query.offset as string) : undefined,
      };

      const dishes = await storage.searchDishes(searchParams);
      res.json(dishes);
    } else {
      // Get all dishes with chef information
      const dishes = await storage.getDishesWithChef();
      res.json(dishes);
    }
  } catch (error) {
    console.error('Error getting dishes:', error);
    res.status(500).json({ error: 'Failed to get dishes' });
  }
});

// Get dish by ID with chef information
router.get('/:id', async (req, res) => {
  try {
    const dishId = parseInt(req.params.id);
    const dish = await storage.getDish(dishId);

    if (!dish) {
      return res.status(404).json({ error: 'Dish not found' });
    }

    // Get chef information
    const chef = await storage.getUser(dish.chefId);
    const dishWithChef = {
      ...dish,
      chef: chef || null
    };

    res.json(dishWithChef);
  } catch (error) {
    console.error('Error getting dish:', error);
    res.status(500).json({ error: 'Failed to get dish' });
  }
});

// Create new dish (chef only)
router.post('/', authenticateToken, requireRole(['chef']), async (req, res) => {
  try {
    const chefId = req.user!.userId;
    const dishData: InsertDish = {
      ...req.body,
      chefId,
    };

    const dish = await storage.createDish(dishData);
    res.status(201).json(dish);
  } catch (error) {
    console.error('Error creating dish:', error);
    res.status(500).json({ error: 'Failed to create dish' });
  }
});

// Update dish (chef only, owner only)
router.put('/:id', authenticateToken, requireRole(['chef']), async (req, res) => {
  try {
    const dishId = parseInt(req.params.id);
    const chefId = req.user!.userId;
    
    // Check if dish exists and belongs to the chef
    const existingDish = await storage.getDish(dishId);
    if (!existingDish) {
      return res.status(404).json({ error: 'Dish not found' });
    }
    
    if (existingDish.chefId !== chefId) {
      return res.status(403).json({ error: 'Not authorized to update this dish' });
    }

    const dishData: Partial<InsertDish> = req.body;
    const updatedDish = await storage.updateDish(dishId, dishData);
    res.json(updatedDish);
  } catch (error) {
    console.error('Error updating dish:', error);
    res.status(500).json({ error: 'Failed to update dish' });
  }
});

// Delete dish (chef only, owner only)
router.delete('/:id', authenticateToken, requireRole(['chef']), async (req, res) => {
  try {
    const dishId = parseInt(req.params.id);
    const chefId = req.user!.userId;
    
    // Check if dish exists and belongs to the chef
    const existingDish = await storage.getDish(dishId);
    if (!existingDish) {
      return res.status(404).json({ error: 'Dish not found' });
    }
    
    if (existingDish.chefId !== chefId) {
      return res.status(403).json({ error: 'Not authorized to delete this dish' });
    }

    await storage.deleteDish(dishId);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting dish:', error);
    res.status(500).json({ error: 'Failed to delete dish' });
  }
});

// Update dish status (chef only, owner only)
router.patch('/:id/status', authenticateToken, requireRole(['chef']), async (req, res) => {
  try {
    const dishId = parseInt(req.params.id);
    const chefId = req.user!.userId;
    const { status } = req.body;
    
    if (!status || !['draft', 'active', 'inactive'].includes(status)) {
      return res.status(400).json({ error: 'Invalid status. Must be draft, active, or inactive' });
    }
    
    // Check if dish exists and belongs to the chef
    const existingDish = await storage.getDish(dishId);
    if (!existingDish) {
      return res.status(404).json({ error: 'Dish not found' });
    }
    
    if (existingDish.chefId !== chefId) {
      return res.status(403).json({ error: 'Not authorized to update this dish' });
    }

    await storage.updateDishStatus(dishId, status);
    res.json({ message: 'Dish status updated successfully' });
  } catch (error) {
    console.error('Error updating dish status:', error);
    res.status(500).json({ error: 'Failed to update dish status' });
  }
});

// Get dishes by category
router.get('/category/:category', async (req, res) => {
  try {
    const category = req.params.category;
    const dishes = await storage.getDishesByCategory(category);
    res.json(dishes);
  } catch (error) {
    console.error('Error getting dishes by category:', error);
    res.status(500).json({ error: 'Failed to get dishes by category' });
  }
});

// Get dishes by region
router.get('/region/:region', async (req, res) => {
  try {
    const region = req.params.region;
    const dishes = await storage.getDishesByRegion(region);
    res.json(dishes);
  } catch (error) {
    console.error('Error getting dishes by region:', error);
    res.status(500).json({ error: 'Failed to get dishes by region' });
  }
});

// Get dishes by tags
router.get('/tags/:tags', async (req, res) => {
  try {
    const tags = req.params.tags.split(',');
    const dishes = await storage.getDishesByTags(tags);
    res.json(dishes);
  } catch (error) {
    console.error('Error getting dishes by tags:', error);
    res.status(500).json({ error: 'Failed to get dishes by tags' });
  }
});

// Get dish categories
router.get('/categories/all', async (req, res) => {
  try {
    const categories = await storage.getDishCategories();
    res.json(categories);
  } catch (error) {
    console.error('Error getting dish categories:', error);
    res.status(500).json({ error: 'Failed to get dish categories' });
  }
});

// Create dish category (admin only)
router.post('/categories', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const category = await storage.createDishCategory(req.body);
    res.status(201).json(category);
  } catch (error) {
    console.error('Error creating dish category:', error);
    res.status(500).json({ error: 'Failed to create dish category' });
  }
});

// Update dish category (admin only)
router.put('/categories/:id', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const categoryId = parseInt(req.params.id);
    const updatedCategory = await storage.updateDishCategory(categoryId, req.body);
    res.json(updatedCategory);
  } catch (error) {
    console.error('Error updating dish category:', error);
    res.status(500).json({ error: 'Failed to update dish category' });
  }
});

// Delete dish category (admin only)
router.delete('/categories/:id', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const categoryId = parseInt(req.params.id);
    await storage.deleteDishCategory(categoryId);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting dish category:', error);
    res.status(500).json({ error: 'Failed to delete dish category' });
  }
});

export default router; 