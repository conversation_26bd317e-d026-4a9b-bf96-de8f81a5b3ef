import '@testing-library/jest-dom';

// Mock WebSocket
class MockWebSocket {
  onopen: (() => void) | null = null;
  onmessage: ((event: { data: string }) => void) | null = null;
  onclose: (() => void) | null = null;
  onerror: ((error: any) => void) | null = null;
  readyState = 1; // OPEN

  constructor() {}

  send(data: string) {}
  close() {}
}

global.WebSocket = MockWebSocket as any;

// Mock fetch
global.fetch = jest.fn();

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

global.localStorage = localStorageMock as any;

// Polyfill TextEncoder/TextDecoder for Node.js
import { TextEncoder, TextDecoder } from 'util';
(globalThis as any).TextEncoder = TextEncoder;
(globalThis as any).TextDecoder = TextDecoder;

// Mock ws.WebSocketServer for tests
(globalThis as any).WebSocketServer = class {
  constructor() {}
  on() {}
  close() {}
};

jest.mock('ws', () => ({
  WebSocketServer: (globalThis as any).WebSocketServer,
  WebSocket: class {},
}));

// Polyfill setImmediate if not present
if (typeof (globalThis as any).setImmediate === 'undefined') {
  const setImmediatePolyfill = (fn: (...args: any[]) => void, ...args: any[]) => setTimeout(fn, 0, ...args);
  (setImmediatePolyfill as any).__promisify__ = function() {};
  (globalThis as any).setImmediate = setImmediatePolyfill;
} 