import request from 'supertest';
import express from 'express';
import { registerRoutes } from './routes';

describe('API Routes', () => {
  let app: express.Express;
  let server: any;

  beforeAll(async () => {
    app = express();
    app.use(express.json());
    server = await registerRoutes(app);
  });

  afterAll(() => {
    server.close();
  });

  describe('Dishes API', () => {
    it('should get all dishes', async () => {
      const response = await request(app).get('/api/dishes');
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    it('should get dish by ID', async () => {
      const response = await request(app).get('/api/dishes/1');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', 1);
      expect(response.body).toHaveProperty('chef');
    });

    it('should return 404 for non-existent dish', async () => {
      const response = await request(app).get('/api/dishes/999');
      expect(response.status).toBe(404);
    });
  });

  describe('Orders API', () => {
    it('should create a new order', async () => {
      const newOrder = {
        userId: 1,
        dishId: 1,
        chefId: 1,
        totalAmount: '15.99',
        customization: { spiceLevel: 'medium' },
        deliveryAddress: '123 Test St'
      };

      const response = await request(app)
        .post('/api/orders')
        .send(newOrder);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.status).toBe('pending');
    });

    it('should get orders by user', async () => {
      const response = await request(app).get('/api/orders/user/1');
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('Votes API', () => {
    it('should create a new vote', async () => {
      const newVote = {
        userId: 1,
        dishId: 1,
        customization: { spiceLevel: 'medium' }
      };

      const response = await request(app)
        .post('/api/votes')
        .send(newVote);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
    });

    it('should check if user has voted', async () => {
      const response = await request(app).get('/api/votes/check/1/1');
      expect(response.status).toBe(200);
      expect(typeof response.body.hasVoted).toBe('boolean');
    });
  });
}); 