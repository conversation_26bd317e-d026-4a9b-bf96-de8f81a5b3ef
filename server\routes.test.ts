import request from 'supertest';
import express from 'express';
import { registerRoutes } from './routes';
import { storage } from './storage';

// Helper to seed a chef with id: 1
async function seedChefWithId1() {
  const chef = await storage.getUser(1);
  if (!chef) {
    await storage.createUser({
      username: 'seedchef',
      password: 'password123',
      email: '<EMAIL>',
      name: 'Seed Chef',
      role: 'chef',
      region: 'Test Region'
    });
  }
}

describe('API Routes', () => {
  let app: express.Express;
  let server: any;

  beforeAll(async () => {
    app = express();
    app.use(express.json());
    server = await registerRoutes(app);
  });

  afterAll(() => {
    if (server && typeof server.close === 'function') {
      server.close();
    }
  });

  describe('Dishes API', () => {
    let seededChefId: number;
    let seededDishId: number;
    beforeAll(async () => {
      // Create a chef and get their ID
      const chef = await storage.createUser({
        username: 'seedchef',
        password: 'password123',
        email: '<EMAIL>',
        name: 'Seed Chef',
        role: 'chef',
        region: 'Test Region'
      });
      seededChefId = chef.id;
      // Create a dish with the chef's ID
      const dish = await storage.createDish({
        chefId: seededChefId,
        name: 'Seed Dish',
        description: 'A seeded dish for tests',
        price: '10.00',
        image: 'test.jpg',
        region: 'Test Region',
        cookingTime: 30,
        minOrders: 1,
        category: 'Main Course'
      });
      seededDishId = dish.id;
    });

    it('should get all dishes', async () => {
      const response = await request(app).get('/api/dishes');
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    it('should get dish by ID', async () => {
      const response = await request(app)
        .get(`/api/dishes/${seededDishId}`)
        .expect(200);

      console.log('Dish by ID response:', response.body);

      expect(response.body).toHaveProperty('id', seededDishId);
      expect(response.body).toHaveProperty('name');
      expect(response.body).toHaveProperty('description');
      expect(response.body).toHaveProperty('price');
      expect(response.body).toHaveProperty('chef');
      expect(response.body.chef).toBeDefined();
      expect(response.body.chef).toHaveProperty('id', seededChefId);
    });

    it('should return 404 for non-existent dish', async () => {
      const response = await request(app).get('/api/dishes/999');
      expect(response.status).toBe(404);
    });
  });

  describe('Orders API', () => {
    it('should create a new order', async () => {
      const newOrder = {
        userId: 1,
        dishId: 1,
        chefId: 1,
        totalAmount: '15.99',
        customization: { spiceLevel: 'medium' },
        deliveryAddress: '123 Test St'
      };

      const response = await request(app)
        .post('/api/orders')
        .send(newOrder);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.status).toBe('pending');
    });

    it('should get orders by user', async () => {
      const response = await request(app).get('/api/users/1/orders');
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('Votes API', () => {
    it('should create a new vote', async () => {
      const newVote = {
        userId: 1,
        dishId: 1,
        customization: { spiceLevel: 'medium' }
      };

      const response = await request(app)
        .post('/api/votes')
        .send(newVote);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
    });

    it('should check if user has voted', async () => {
      // First create a vote
      const newVote = {
        userId: 2,
        dishId: 1,
        customization: { spiceLevel: 'medium' }
      };
      await request(app).post('/api/votes').send(newVote);

      // Then check if user has voted by calling the storage directly
      // Since there's no direct API endpoint for this, we'll test the vote creation instead
      const response = await request(app)
        .post('/api/votes')
        .send(newVote);

      // Should fail because user already voted
      expect(response.status).toBe(400);
      expect(response.body.message).toBe('User has already voted for this dish');
    });
  });

  describe('Chefs API', () => {
    it('should get all chefs', async () => {
      const response = await request(app).get('/api/chefs');
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body.every((chef: any) => chef.role === 'chef')).toBe(true);
    });

    it('should get featured chefs', async () => {
      const response = await request(app).get('/api/chefs/featured');
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
    });
  });
}); 