import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Clock, MapPin, ChefHat, Star, Package, CheckCircle, XCircle } from "lucide-react";
import { useWebSocket } from "@/hooks/use-websocket";
import type { OrderWithDetails } from "@shared/schema";
import { ORDER_STATUSES } from "@/lib/constants";

interface OrderHistoryProps {
  userId: number;
}

export default function OrderHistory({ userId }: OrderHistoryProps) {
  const [statusFilter, setStatusFilter] = useState<string>("all");
  
  // Real-time WebSocket connection
  useWebSocket();

  // Fetch user orders
  const { data: orders = [], isLoading } = useQuery<OrderWithDetails[]>({
    queryKey: [`/api/users/${userId}/orders`],
  });

  // Filter orders based on status
  const filteredOrders = statusFilter === "all" 
    ? orders 
    : orders.filter(order => order.status === statusFilter);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-500";
      case "cooking":
        return "bg-orange-500";
      case "ready":
        return "bg-blue-500";
      case "delivering":
        return "bg-purple-500";
      case "completed":
        return "bg-green-500";
      case "cancelled":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="w-4 h-4" />;
      case "cooking":
        return <ChefHat className="w-4 h-4" />;
      case "ready":
        return <Package className="w-4 h-4" />;
      case "delivering":
        return <MapPin className="w-4 h-4" />;
      case "completed":
        return <CheckCircle className="w-4 h-4" />;
      case "cancelled":
        return <XCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) return "N/A";
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEstimatedDelivery = (order: OrderWithDetails) => {
    if (order.estimatedDelivery) {
      return formatDate(order.estimatedDelivery);
    }
    
    if (!order.createdAt) {
      return "N/A";
    }
    
    // Calculate estimated delivery based on cooking time and status
    const cookingTime = order.dish.cookingTime || 30;
    const orderDate = new Date(order.createdAt);
    const estimatedTime = new Date(orderDate.getTime() + (cookingTime * 60 * 1000));
    
    return formatDate(estimatedTime);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-[var(--secondary-dark)] rounded-lg p-6">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gray-600 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-600 rounded mb-2 w-32"></div>
                  <div className="h-3 bg-gray-600 rounded mb-1 w-24"></div>
                  <div className="h-3 bg-gray-600 rounded w-20"></div>
                </div>
                <div className="text-right">
                  <div className="h-4 bg-gray-600 rounded mb-1 w-16"></div>
                  <div className="h-3 bg-gray-600 rounded w-20"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filter Controls */}
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold">Order History</h3>
        <div className="flex items-center space-x-4">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Orders</SelectItem>
              {ORDER_STATUSES.map(status => (
                <SelectItem key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Orders List */}
      {filteredOrders.length === 0 ? (
        <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
          <CardContent className="p-8 text-center">
            <Package className="w-12 h-12 mx-auto mb-4 text-[var(--text-secondary)] opacity-50" />
            <h4 className="text-lg font-semibold mb-2">No Orders Found</h4>
            <p className="text-[var(--text-secondary)]">
              {statusFilter === "all" 
                ? "You haven't placed any orders yet." 
                : `No ${statusFilter} orders found.`
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredOrders.map((order) => (
            <Card key={order.id} className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <img
                      src={order.dish.image}
                      alt={order.dish.name}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-semibold text-lg">{order.dish.name}</h4>
                        <Badge className={getStatusColor(order.status)}>
                          {getStatusIcon(order.status)}
                          <span className="ml-1">
                            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                          </span>
                        </Badge>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-[var(--text-secondary)] mb-2">
                        <div className="flex items-center">
                          <ChefHat className="w-4 h-4 mr-1" />
                          <span>by {order.chef.name}</span>
                        </div>
                        <div className="flex items-center">
                          <Star className="w-4 h-4 mr-1 text-yellow-400 fill-current" />
                          <span>{order.chef.rating?.toString() || 'N/A'}</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-1" />
                          <span>{order.dish.region}</span>
                        </div>
                      </div>

                      {order.customization && (
                        <div className="text-sm text-[var(--text-secondary)] mb-2">
                          <span className="font-medium">Customizations:</span>
                          <span className="ml-1">
                            {Object.entries(order.customization)
                              .map(([key, value]) => `${key}: ${value}`)
                              .join(', ')}
                          </span>
                        </div>
                      )}

                      <div className="text-sm text-[var(--text-secondary)]">
                        <span>Ordered: {order.createdAt ? formatDate(order.createdAt) : 'N/A'}</span>
                        {order.status !== "completed" && order.status !== "cancelled" && (
                          <span className="ml-4">
                            Estimated delivery: {getEstimatedDelivery(order)}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="text-xl font-bold text-[var(--accent-green)] mb-2">
                      ${order.totalAmount}
                    </div>
                    <div className="text-sm text-[var(--text-secondary)]">
                      Order #{order.id}
                    </div>
                    
                    {order.status === "pending" && (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-2 btn-secondary"
                      >
                        Cancel Order
                      </Button>
                    )}
                    
                    {order.status === "completed" && (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-2 btn-primary"
                      >
                        Rate & Review
                      </Button>
                    )}
                  </div>
                </div>

                {/* Order Progress Timeline */}
                {order.status !== "completed" && order.status !== "cancelled" && (
                  <div className="mt-4 pt-4 border-t border-[var(--border-gray)]">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Order Placed</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${
                          ['cooking', 'ready', 'delivering', 'completed'].includes(order.status) 
                            ? 'bg-green-500' 
                            : 'bg-gray-400'
                        }`}></div>
                        <span>Cooking Started</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${
                          ['ready', 'delivering', 'completed'].includes(order.status) 
                            ? 'bg-green-500' 
                            : 'bg-gray-400'
                        }`}></div>
                        <span>Ready for Delivery</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${
                          ['delivering', 'completed'].includes(order.status) 
                            ? 'bg-green-500' 
                            : 'bg-gray-400'
                        }`}></div>
                        <span>Out for Delivery</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${
                          order.status === 'completed' 
                            ? 'bg-green-500' 
                            : 'bg-gray-400'
                        }`}></div>
                        <span>Delivered</span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
} 