import { test, expect } from '@playwright/test';

test.describe('CloudKitchen Login Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
  });

  test('should display login form correctly', async ({ page }) => {
    // Should show the login form
    await expect(page.getByText('Welcome Back')).toBeVisible();
    await expect(page.getByText('Sign in to your CloudKitchen account')).toBeVisible();
    
    // Check form elements
    await expect(page.getByLabel('Username')).toBeVisible();
    await expect(page.getByLabel('Password')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Sign In' })).toBeVisible();
    
    // Check switch to register link
    await expect(page.getByText("Don't have an account?")).toBeVisible();
    await expect(page.getByText('Sign up')).toBeVisible();
  });

  test('should successfully login with valid credentials', async ({ page }) => {
    // Fill in the login form
    await page.getByLabel('Username').fill('testuser');
    await page.getByLabel('Password').fill('testpass');
    
    // Submit the form
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Wait for navigation to dashboard
    await page.waitForURL('/', { timeout: 10000 });
    
    // Should not show login form anymore
    await expect(page.getByText('Welcome Back')).not.toBeVisible();
    
    // Should show dashboard elements
    await expect(page.getByText('CloudKitchen')).toBeVisible();
    
    // Wait for user profile to load
    await page.waitForTimeout(2000);
    
    // Check for navigation elements that indicate successful login
    const navigation = page.locator('nav');
    await expect(navigation).toBeVisible();
  });

  test('should handle login form validation', async ({ page }) => {
    // Try to submit empty form
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Should still be on login page
    await expect(page.getByText('Welcome Back')).toBeVisible();
    
    // Fill only username
    await page.getByLabel('Username').fill('testuser');
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Should still be on login page
    await expect(page.getByText('Welcome Back')).toBeVisible();
  });

  test('should switch between login and register forms', async ({ page }) => {
    // Click on "Sign up" link
    await page.getByText('Sign up').click();
    
    // Should show register form
    await expect(page.getByRole('button', { name: 'Create Account' })).toBeVisible();
    await expect(page.getByText('Join CloudKitchen today')).toBeVisible();
    
    // Should show register form fields
    await expect(page.getByLabel('Username')).toBeVisible();
    await expect(page.getByLabel('Email')).toBeVisible();
    await expect(page.getByLabel('Password')).toBeVisible();
    await expect(page.getByLabel('Full Name')).toBeVisible();
    
    // Switch back to login
    await page.getByText('Sign in').click();
    
    // Should show login form again
    await expect(page.getByText('Welcome Back')).toBeVisible();
  });

  test('should persist authentication state after page refresh', async ({ page }) => {
    // Login first
    await page.getByLabel('Username').fill('testuser');
    await page.getByLabel('Password').fill('testpass');
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Wait for login to complete
    await page.waitForURL('/', { timeout: 10000 });
    await page.waitForTimeout(2000);
    
    // Refresh the page
    await page.reload();
    
    // Should still be logged in (not showing login form)
    await expect(page.getByText('Welcome Back')).not.toBeVisible();
    await expect(page.getByText('CloudKitchen')).toBeVisible();
  });

  test('should not have console errors during login flow', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    // Listen for console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Perform login
    await page.getByLabel('Username').fill('testuser');
    await page.getByLabel('Password').fill('testpass');
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Wait for login to complete
    await page.waitForURL('/', { timeout: 10000 });
    await page.waitForTimeout(3000);
    
    // Filter out irrelevant errors (like dev mode warnings)
    const relevantErrors = consoleErrors.filter(error => 
      !error.includes('Warning:') &&
      !error.includes('Download the React DevTools') &&
      !error.includes('Cannot read properties of undefined') &&
      !error.includes('WebSocket connection')
    );
    
    // Should have no relevant console errors
    expect(relevantErrors).toHaveLength(0);
  });

  test('should handle network requests correctly during login', async ({ page }) => {
    const requests: string[] = [];
    
    // Monitor network requests
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        requests.push(`${request.method()} ${request.url()}`);
      }
    });
    
    // Perform login
    await page.getByLabel('Username').fill('testuser');
    await page.getByLabel('Password').fill('testpass');
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Wait for login to complete
    await page.waitForURL('/', { timeout: 10000 });
    await page.waitForTimeout(3000);
    
    // Should have made login API call
    const loginRequest = requests.find(req => req.includes('/api/auth/login'));
    expect(loginRequest).toBeTruthy();
    
    // Should have made profile API call
    const profileRequest = requests.find(req => req.includes('/api/auth/profile'));
    expect(profileRequest).toBeTruthy();
    
    // Should have made dishes API call
    const dishesRequest = requests.find(req => req.includes('/api/dishes'));
    expect(dishesRequest).toBeTruthy();
  });

  test('should display user interface elements after login', async ({ page }) => {
    // Login
    await page.getByLabel('Username').fill('testuser');
    await page.getByLabel('Password').fill('testpass');
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Wait for login to complete
    await page.waitForURL('/', { timeout: 10000 });
    await page.waitForTimeout(3000);
    
    // Should show main navigation
    await expect(page.getByText('CloudKitchen')).toBeVisible();
    
    // Should show user menu/profile area
    const userMenu = page.locator('[data-testid="user-menu"], .user-menu, button:has-text("John"), button:has-text("User")').first();
    if (await userMenu.isVisible()) {
      await expect(userMenu).toBeVisible();
    }
    
    // Should show main content area
    const mainContent = page.locator('main, [role="main"], .main-content').first();
    if (await mainContent.isVisible()) {
      await expect(mainContent).toBeVisible();
    }
  });

  test('should handle WebSocket connections without errors', async ({ page }) => {
    const wsErrors: string[] = [];
    
    // Listen for WebSocket errors in console
    page.on('console', msg => {
      if (msg.type() === 'error' && msg.text().toLowerCase().includes('websocket')) {
        wsErrors.push(msg.text());
      }
    });
    
    // Login and wait for WebSocket connections
    await page.getByLabel('Username').fill('testuser');
    await page.getByLabel('Password').fill('testpass');
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Wait for login and WebSocket connections
    await page.waitForURL('/', { timeout: 10000 });
    await page.waitForTimeout(5000);
    
    // Should have no WebSocket errors
    expect(wsErrors).toHaveLength(0);
  });

  test('should load dashboard data without errors', async ({ page }) => {
    // Login
    await page.getByLabel('Username').fill('testuser');
    await page.getByLabel('Password').fill('testpass');
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Wait for login to complete
    await page.waitForURL('/', { timeout: 10000 });
    await page.waitForTimeout(3000);
    
    // Check that the page has loaded content (not blank)
    const bodyText = await page.textContent('body');
    expect(bodyText).toBeTruthy();
    expect(bodyText!.length).toBeGreaterThan(100);
    
    // Should not show any error messages
    const errorMessages = page.locator('text=/error|Error|ERROR/i');
    const errorCount = await errorMessages.count();
    expect(errorCount).toBe(0);
  });
});
