import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, CheckCircle, Clock } from "lucide-react";
import { useWebSocket } from "@/hooks/use-websocket";
import type { User, OrderWithDetails } from "@shared/schema";

interface DashboardStats {
  activeChefs: number;
  totalCustomers: number;
  activeDishes: number;
  pendingOrders: number;
  monthlyRevenue: string;
}

export default function AdminView() {
  // Real-time WebSocket connection
  useWebSocket();

  // Fallback mock data
  const mockStats: DashboardStats = {
    activeChefs: 12,
    totalCustomers: 245,
    activeDishes: 38,
    pendingOrders: 15,
    monthlyRevenue: "₹45,230"
  };

  const mockChefs: User[] = [
    {
      id: 1,
      name: "<PERSON> <PERSON><PERSON>",
      email: "<EMAIL>",
      username: "chef_ram<PERSON>",
      role: "chef",
      region: "Hyderabad",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100",
      rating: "4.8",
      totalOrders: 150,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // Fetch dashboard stats
  const { data: fetchedStats } = useQuery<DashboardStats>({
    queryKey: ["/api/stats"],
  });

  // Fetch all chefs
  const { data: fetchedChefs = [], isLoading: chefsLoading } = useQuery<User[]>({
    queryKey: ["/api/chefs"],
  });

  // Fetch all orders
  const { data: fetchedOrders = [], isLoading: ordersLoading } = useQuery<OrderWithDetails[]>({
    queryKey: ["/api/orders"],
  });

  // Use fetched data if available, otherwise use mock data
  const stats = fetchedStats || mockStats;
  const chefs = fetchedChefs.length > 0 ? fetchedChefs : mockChefs;
  const orders = fetchedOrders;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Admin Dashboard Header */}
      <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)] mb-8">
        <CardContent className="p-6">
          <h2 className="text-2xl font-bold mb-4">Admin Dashboard</h2>
          <div className="grid grid-cols-1 sm:grid-cols-5 gap-4">
            <div className="bg-[var(--primary-dark)] rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-[var(--accent-green)] mb-1">
                {stats?.activeChefs || 0}
              </div>
              <div className="text-[var(--text-secondary)] text-sm">Active Chefs</div>
            </div>
            <div className="bg-[var(--primary-dark)] rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-blue-400 mb-1">
                {stats?.totalCustomers || 0}
              </div>
              <div className="text-[var(--text-secondary)] text-sm">Customers</div>
            </div>
            <div className="bg-[var(--primary-dark)] rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-purple-400 mb-1">
                {stats?.activeDishes || 0}
              </div>
              <div className="text-[var(--text-secondary)] text-sm">Active Dishes</div>
            </div>
            <div className="bg-[var(--primary-dark)] rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-yellow-400 mb-1">
                {stats?.pendingOrders || 0}
              </div>
              <div className="text-[var(--text-secondary)] text-sm">Pending Orders</div>
            </div>
            <div className="bg-[var(--primary-dark)] rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-purple-400 mb-1">
                ${stats?.monthlyRevenue || "0"}
              </div>
              <div className="text-[var(--text-secondary)] text-sm">Monthly Revenue</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chef Management */}
      <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)] mb-8">
        <CardContent className="p-6">
          <h3 className="text-xl font-bold mb-6">Chef Management</h3>
          {chefsLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse border-b border-[var(--border-gray)] py-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-600 rounded-full"></div>
                      <div className="h-4 bg-gray-600 rounded w-24"></div>
                    </div>
                    <div className="h-4 bg-gray-600 rounded w-16"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-[var(--border-gray)]">
                    <th className="text-left py-3">Chef</th>
                    <th className="text-left py-3">Region</th>
                    <th className="text-left py-3">Rating</th>
                    <th className="text-left py-3">Orders</th>
                    <th className="text-left py-3">Status</th>
                    <th className="text-left py-3">Actions</th>
                  </tr>
                </thead>
                <tbody className="text-sm">
                  {chefs.map((chef) => (
                    <tr key={chef.id} className="border-b border-[var(--border-gray)]">
                      <td className="py-3">
                        <div className="flex items-center space-x-3">
                          <img
                            src={chef.avatar || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&w=40&h=40&fit=crop&crop=face"}
                            alt={chef.name}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                          <span>{chef.name}</span>
                        </div>
                      </td>
                      <td className="py-3">{chef.region}</td>
                      <td className="py-3">
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-400 mr-1 fill-current" />
                          <span>{chef.rating}</span>
                        </div>
                      </td>
                      <td className="py-3">{chef.totalOrders}</td>
                      <td className="py-3">
                        <Badge variant={chef.isActive ? "default" : "secondary"}>
                          {chef.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </td>
                      <td className="py-3">
                        <Button variant="link" size="sm" className="text-[var(--accent-green)] p-0 mr-3">
                          View
                        </Button>
                        <Button variant="link" size="sm" className="text-[var(--text-secondary)] p-0">
                          Edit
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Order Feasibility */}
      <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
        <CardContent className="p-6">
          <h3 className="text-xl font-bold mb-6">Order Feasibility Monitor</h3>
          {ordersLoading ? (
            <div className="space-y-4">
              {[...Array(2)].map((_, i) => (
                <div key={i} className="animate-pulse border border-[var(--border-gray)] rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gray-600 rounded-lg"></div>
                      <div>
                        <div className="h-4 bg-gray-600 rounded mb-2 w-24"></div>
                        <div className="h-3 bg-gray-600 rounded w-32"></div>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <div className="h-6 bg-gray-600 rounded w-20"></div>
                      <div className="h-6 bg-gray-600 rounded w-20"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {orders
                .filter(order => order.status === "pending")
                .map((order) => (
                  <div key={order.id} className="border border-[var(--border-gray)] rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <img
                          src={order.dish.image}
                          alt={order.dish.name}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                        <div>
                          <h4 className="font-semibold">{order.dish.name}</h4>
                          <p className="text-[var(--text-secondary)] text-sm">
                            by {order.chef.name} • Order #{order.id}
                          </p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Badge className="bg-[var(--accent-green)] text-white">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Ingredients
                        </Badge>
                        <Badge className="bg-[var(--accent-green)] text-white">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Logistics
                        </Badge>
                        <Badge className="bg-[var(--accent-green)] text-white">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Profit
                        </Badge>
                      </div>
                    </div>
                    <Button className="btn-primary">
                      Approve Cooking
                    </Button>
                  </div>
                ))}
              {orders.filter(order => order.status === "pending").length === 0 && (
                <div className="text-center py-8 text-[var(--text-secondary)]">
                  <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No pending orders requiring approval</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
