services:
  - type: web
    name: cloudkitchen
    runtime: docker
    plan: starter
    region: oregon
    healthCheckPath: /api/dishes
    envVars:
      - key: NODE_ENV
        value: production
      - key: DATABASE_URL
        sync: false
      - key: JWT_SECRET
        sync: false
      - key: SESSION_SECRET
        sync: false
    buildCommand: docker build -t cloudkitchen .
    startCommand: docker run -p $PORT:5000 cloudkitchen

databases:
  - name: cloudkitchen-db
    databaseName: cloudkitchen
    user: cloudkitchen
    plan: starter 