import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider } from '@/context/AuthContext';
import { LoginForm } from '@/components/auth/LoginForm';
import App from '@/App';

// Mock fetch for API calls
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock WebSocket
class MockWebSocket {
  onopen: (() => void) | null = null;
  onmessage: ((event: { data: string }) => void) | null = null;
  onclose: (() => void) | null = null;
  onerror: ((error: any) => void) | null = null;
  readyState = 1; // OPEN
  url: string;

  constructor(url: string) {
    this.url = url;
    // Simulate successful connection
    setTimeout(() => {
      if (this.onopen) {
        this.onopen();
      }
    }, 10);
  }

  send(data: string) {
    // Echo back for testing
    setTimeout(() => {
      if (this.onmessage) {
        this.onmessage({ data: JSON.stringify({ type: 'echo', data: JSON.parse(data) }) });
      }
    }, 10);
  }

  close() {
    setTimeout(() => {
      if (this.onclose) {
        this.onclose();
      }
    }, 10);
  }
}

global.WebSocket = MockWebSocket as any;

// Mock console to capture errors
const originalConsoleError = console.error;
const consoleErrors: string[] = [];

beforeEach(() => {
  consoleErrors.length = 0;
  console.error = (...args: any[]) => {
    consoleErrors.push(args.join(' '));
    originalConsoleError(...args);
  };
});

afterEach(() => {
  console.error = originalConsoleError;
  jest.clearAllMocks();
});

// Helper function to create a proper JWT token structure
function createMockJWT() {
  const header = { alg: 'HS256', typ: 'JWT' };
  const payload = {
    userId: 1,
    username: 'testuser',
    role: 'customer',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  };
  
  const encodedHeader = btoa(JSON.stringify(header));
  const encodedPayload = btoa(JSON.stringify(payload));
  const mockSignature = 'mock-signature';
  
  return `${encodedHeader}.${encodedPayload}.${mockSignature}`;
}

// Mock user data that matches the schema
const mockUser = {
  id: 1,
  name: "John Doe",
  email: "<EMAIL>",
  username: "testuser",
  password: "password",
  role: "customer",
  region: "North America",
  avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100",
  rating: "4.50",
  totalOrders: 12,
  isActive: true
};

describe('CloudKitchen Login Flow E2E', () => {
  describe('Login Form Component', () => {
    it('should render login form correctly', () => {
      const mockOnSuccess = jest.fn();
      const mockOnSwitchToRegister = jest.fn();

      render(
        <AuthProvider>
          <LoginForm onSuccess={mockOnSuccess} onSwitchToRegister={mockOnSwitchToRegister} />
        </AuthProvider>
      );

      expect(screen.getByText('Welcome Back')).toBeInTheDocument();
      expect(screen.getByText('Sign in to your CloudKitchen account')).toBeInTheDocument();
      expect(screen.getByLabelText('Username')).toBeInTheDocument();
      expect(screen.getByLabelText('Password')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument();
    });

    it('should handle form input correctly', async () => {
      const user = userEvent.setup();
      const mockOnSuccess = jest.fn();

      render(
        <AuthProvider>
          <LoginForm onSuccess={mockOnSuccess} />
        </AuthProvider>
      );

      const usernameInput = screen.getByLabelText('Username');
      const passwordInput = screen.getByLabelText('Password');

      await user.type(usernameInput, 'testuser');
      await user.type(passwordInput, 'testpass');

      expect(usernameInput).toHaveValue('testuser');
      expect(passwordInput).toHaveValue('testpass');
    });
  });

  describe('Authentication Flow', () => {
    it('should successfully login with valid credentials', async () => {
      const user = userEvent.setup();
      const mockJWT = createMockJWT();

      // Mock successful login response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          user: mockUser,
          token: mockJWT,
          expiresIn: 24 * 60 * 60
        })
      });

      const mockOnSuccess = jest.fn();

      render(
        <AuthProvider>
          <LoginForm onSuccess={mockOnSuccess} />
        </AuthProvider>
      );

      // Fill in the form
      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByLabelText('Password'), 'testpass');

      // Submit the form
      await user.click(screen.getByRole('button', { name: 'Sign In' }));

      // Wait for the login to complete
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: 'testuser',
            password: 'testpass'
          })
        });
      });

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalled();
      });

      // Verify token is stored in localStorage
      expect(localStorage.setItem).toHaveBeenCalledWith('authToken', mockJWT);
    });

    it('should handle login errors gracefully', async () => {
      const user = userEvent.setup();

      // Mock failed login response
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({
          message: 'Invalid credentials'
        })
      });

      render(
        <AuthProvider>
          <LoginForm onSuccess={jest.fn()} />
        </AuthProvider>
      );

      await user.type(screen.getByLabelText('Username'), 'wronguser');
      await user.type(screen.getByLabelText('Password'), 'wrongpass');
      await user.click(screen.getByRole('button', { name: 'Sign In' }));

      await waitFor(() => {
        expect(screen.getByText('Invalid credentials')).toBeInTheDocument();
      });
    });
  });

  describe('JWT Token Validation', () => {
    it('should validate JWT token structure correctly', () => {
      const mockJWT = createMockJWT();
      const parts = mockJWT.split('.');
      
      expect(parts).toHaveLength(3);
      
      // Decode and verify payload
      const payload = JSON.parse(atob(parts[1]));
      expect(payload).toHaveProperty('userId');
      expect(payload).toHaveProperty('username');
      expect(payload).toHaveProperty('role');
      expect(payload).toHaveProperty('exp');
      expect(payload.exp).toBeGreaterThan(Math.floor(Date.now() / 1000));
    });

    it('should handle token expiration correctly', async () => {
      // Create an expired token
      const header = { alg: 'HS256', typ: 'JWT' };
      const payload = {
        userId: 1,
        username: 'testuser',
        role: 'customer',
        iat: Math.floor(Date.now() / 1000) - 3600,
        exp: Math.floor(Date.now() / 1000) - 1800 // Expired 30 minutes ago
      };
      
      const encodedHeader = btoa(JSON.stringify(header));
      const encodedPayload = btoa(JSON.stringify(payload));
      const expiredJWT = `${encodedHeader}.${encodedPayload}.mock-signature`;

      // Mock localStorage to return expired token
      (localStorage.getItem as jest.Mock).mockReturnValue(expiredJWT);

      // Mock profile fetch to fail with expired token
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        json: async () => ({ message: 'Token expired' })
      });

      render(
        <AuthProvider>
          <div>Test Component</div>
        </AuthProvider>
      );

      await waitFor(() => {
        expect(localStorage.removeItem).toHaveBeenCalledWith('authToken');
      });
    });
  });

  describe('WebSocket Connectivity', () => {
    it('should establish WebSocket connection without errors', async () => {
      let wsInstance: MockWebSocket | null = null;
      
      // Override WebSocket constructor to capture instance
      const OriginalWebSocket = global.WebSocket;
      global.WebSocket = class extends MockWebSocket {
        constructor(url: string) {
          super(url);
          wsInstance = this;
        }
      } as any;

      // Simulate WebSocket connection in a component
      const TestComponent = () => {
        React.useEffect(() => {
          const ws = new WebSocket('ws://localhost:5000/ws');
          return () => ws.close();
        }, []);
        return <div>WebSocket Test</div>;
      };

      render(<TestComponent />);

      await waitFor(() => {
        expect(wsInstance).not.toBeNull();
        expect(wsInstance?.url).toBe('ws://localhost:5000/ws');
      });

      // Restore original WebSocket
      global.WebSocket = OriginalWebSocket;
    });
  });

  describe('Console Error Validation', () => {
    it('should not produce console errors during normal login flow', async () => {
      const user = userEvent.setup();
      const mockJWT = createMockJWT();

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          user: mockUser,
          token: mockJWT,
          expiresIn: 24 * 60 * 60
        })
      });

      render(
        <AuthProvider>
          <LoginForm onSuccess={jest.fn()} />
        </AuthProvider>
      );

      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByLabelText('Password'), 'testpass');
      await user.click(screen.getByRole('button', { name: 'Sign In' }));

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalled();
      });

      // Check that no console errors were logged
      const relevantErrors = consoleErrors.filter(error => 
        !error.includes('Warning:') && // Ignore React warnings
        !error.includes('act(') // Ignore act() warnings
      );
      
      expect(relevantErrors).toHaveLength(0);
    });
  });

  describe('User Object Structure Validation', () => {
    it('should handle user object with all required fields', async () => {
      const user = userEvent.setup();
      const mockJWT = createMockJWT();

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          user: mockUser,
          token: mockJWT,
          expiresIn: 24 * 60 * 60
        })
      });

      const TestComponent = () => {
        const { user: authUser } = require('@/context/AuthContext').useAuth();
        
        if (!authUser) return <div>Not logged in</div>;
        
        return (
          <div>
            <div data-testid="user-name">{authUser.name}</div>
            <div data-testid="user-email">{authUser.email}</div>
            <div data-testid="user-rating">{authUser.rating}</div>
            <div data-testid="user-orders">{authUser.totalOrders}</div>
          </div>
        );
      };

      render(
        <AuthProvider>
          <LoginForm onSuccess={jest.fn()} />
          <TestComponent />
        </AuthProvider>
      );

      await user.type(screen.getByLabelText('Username'), 'testuser');
      await user.type(screen.getByLabelText('Password'), 'testpass');
      await user.click(screen.getByRole('button', { name: 'Sign In' }));

      await waitFor(() => {
        expect(screen.getByTestId('user-name')).toHaveTextContent('John Doe');
        expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>');
        expect(screen.getByTestId('user-rating')).toHaveTextContent('4.50');
        expect(screen.getByTestId('user-orders')).toHaveTextContent('12');
      });
    });
  });
});
