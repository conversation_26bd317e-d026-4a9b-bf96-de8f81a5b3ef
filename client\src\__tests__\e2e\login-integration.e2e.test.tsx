/**
 * End-to-End Integration Test for CloudKitchen Login Functionality
 * 
 * This test validates that all the login issues we fixed are working correctly:
 * 1. JWT token structure and validation
 * 2. Authentication flow without console errors
 * 3. WebSocket connectivity
 * 4. User object structure handling
 * 5. Authentication state persistence
 */

import '@testing-library/jest-dom';

// Mock fetch for API calls
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock WebSocket
class MockWebSocket {
  onopen: (() => void) | null = null;
  onmessage: ((event: { data: string }) => void) | null = null;
  onclose: (() => void) | null = null;
  onerror: ((error: any) => void) | null = null;
  readyState = 1; // OPEN
  url: string;

  constructor(url: string) {
    this.url = url;
    setTimeout(() => {
      if (this.onopen) {
        this.onopen();
      }
    }, 10);
  }

  send(data: string) {}
  close() {}
}

global.WebSocket = MockWebSocket as any;

// Helper function to create a proper JWT token structure
function createMockJWT() {
  const header = { alg: 'HS256', typ: 'JWT' };
  const payload = {
    userId: 1,
    username: 'testuser',
    role: 'customer',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  };
  
  const encodedHeader = btoa(JSON.stringify(header));
  const encodedPayload = btoa(JSON.stringify(payload));
  const mockSignature = 'mock-signature';
  
  return `${encodedHeader}.${encodedPayload}.${mockSignature}`;
}

// Mock user data that matches the schema
const mockUser = {
  id: 1,
  name: "John Doe",
  email: "<EMAIL>",
  username: "testuser",
  password: "password",
  role: "customer",
  region: "North America",
  avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100",
  rating: "4.50",
  totalOrders: 12,
  isActive: true
};

const mockDishes = [
  {
    id: 1,
    name: "Spicy Chicken Curry",
    description: "Authentic Indian curry with tender chicken",
    price: "15.99",
    category: "Main Course",
    cuisine: "Indian",
    spiceLevel: 3,
    isVegetarian: false,
    isVegan: false,
    allergens: ["dairy"],
    preparationTime: 25,
    chefId: 1,
    imageUrl: "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400",
    isAvailable: true,
    rating: "4.5",
    reviewCount: 128
  }
];

// Mock console to capture errors
const originalConsoleError = console.error;
const consoleErrors: string[] = [];

beforeEach(() => {
  consoleErrors.length = 0;
  console.error = (...args: any[]) => {
    consoleErrors.push(args.join(' '));
    originalConsoleError(...args);
  };

  // Reset localStorage mocks
  jest.clearAllMocks();
});

afterEach(() => {
  console.error = originalConsoleError;
  jest.clearAllMocks();
});

describe('CloudKitchen Login Integration E2E', () => {
  describe('JWT Token Structure and Validation', () => {
    it('should create properly formatted JWT tokens', () => {
      const mockJWT = createMockJWT();
      const parts = mockJWT.split('.');
      
      expect(parts).toHaveLength(3);
      
      // Decode and verify header
      const header = JSON.parse(atob(parts[0]));
      expect(header.alg).toBe('HS256');
      expect(header.typ).toBe('JWT');
      
      // Decode and verify payload
      const payload = JSON.parse(atob(parts[1]));
      expect(payload).toHaveProperty('userId');
      expect(payload).toHaveProperty('username');
      expect(payload).toHaveProperty('role');
      expect(payload).toHaveProperty('exp');
      expect(payload.exp).toBeGreaterThan(Math.floor(Date.now() / 1000));
    });

    it('should handle token validation correctly', () => {
      const mockJWT = createMockJWT();
      
      // Test that we can decode the token without errors
      const parts = mockJWT.split('.');
      expect(() => {
        const payload = JSON.parse(atob(parts[1]));
        return payload;
      }).not.toThrow();
    });

    it('should detect expired tokens', () => {
      // Create an expired token
      const header = { alg: 'HS256', typ: 'JWT' };
      const payload = {
        userId: 1,
        username: 'testuser',
        role: 'customer',
        iat: Math.floor(Date.now() / 1000) - 3600,
        exp: Math.floor(Date.now() / 1000) - 1800 // Expired 30 minutes ago
      };
      
      const encodedHeader = btoa(JSON.stringify(header));
      const encodedPayload = btoa(JSON.stringify(payload));
      const expiredJWT = `${encodedHeader}.${encodedPayload}.mock-signature`;

      const parts = expiredJWT.split('.');
      const decodedPayload = JSON.parse(atob(parts[1]));
      
      expect(decodedPayload.exp).toBeLessThan(Math.floor(Date.now() / 1000));
    });
  });

  describe('Authentication API Flow', () => {
    it('should handle successful login API call', async () => {
      const mockJWT = createMockJWT();

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          user: mockUser,
          token: mockJWT,
          expiresIn: 24 * 60 * 60
        })
      });

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: 'testuser',
          password: 'testpass'
        })
      });

      const data = await response.json();

      expect(response.ok).toBe(true);
      expect(data).toHaveProperty('user');
      expect(data).toHaveProperty('token');
      expect(data.user).toEqual(mockUser);
      expect(data.token).toBe(mockJWT);
    });

    it('should handle profile fetch with JWT token', async () => {
      const mockJWT = createMockJWT();

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockUser
      });

      const response = await fetch('/api/auth/profile', {
        headers: {
          'Authorization': `Bearer ${mockJWT}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      expect(response.ok).toBe(true);
      expect(data).toEqual(mockUser);
      expect(mockFetch).toHaveBeenCalledWith('/api/auth/profile', {
        headers: {
          'Authorization': `Bearer ${mockJWT}`,
          'Content-Type': 'application/json'
        }
      });
    });

    it('should handle dishes API call', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockDishes
      });

      const response = await fetch('/api/dishes');
      const data = await response.json();

      expect(response.ok).toBe(true);
      expect(Array.isArray(data)).toBe(true);
      expect(data).toEqual(mockDishes);
    });
  });

  describe('User Object Structure Validation', () => {
    it('should handle user object with all required fields', () => {
      // Test that user object has all expected properties
      expect(mockUser).toHaveProperty('id');
      expect(mockUser).toHaveProperty('name');
      expect(mockUser).toHaveProperty('email');
      expect(mockUser).toHaveProperty('username');
      expect(mockUser).toHaveProperty('role');
      expect(mockUser).toHaveProperty('region');
      expect(mockUser).toHaveProperty('avatar');
      expect(mockUser).toHaveProperty('rating');
      expect(mockUser).toHaveProperty('totalOrders');
      expect(mockUser).toHaveProperty('isActive');

      // Test that accessing nested properties doesn't cause errors
      expect(() => {
        const userRating = mockUser.rating;
        const userOrders = mockUser.totalOrders;
        return { userRating, userOrders };
      }).not.toThrow();
    });

    it('should handle user object property access safely', () => {
      // Test the specific property access that was causing errors
      expect(mockUser.rating).toBe("4.50");
      expect(typeof mockUser.rating).toBe("string");
      expect(mockUser.totalOrders).toBe(12);
      expect(typeof mockUser.totalOrders).toBe("number");
    });
  });

  describe('WebSocket Connectivity', () => {
    it('should establish WebSocket connection without errors', async () => {
      let wsInstance: MockWebSocket | null = null;
      
      // Create WebSocket connection
      wsInstance = new MockWebSocket('ws://localhost:5000/ws');

      expect(wsInstance).not.toBeNull();
      expect(wsInstance.url).toBe('ws://localhost:5000/ws');
      expect(wsInstance.readyState).toBe(1); // OPEN

      // Wait for connection to open
      await new Promise(resolve => {
        if (wsInstance!.onopen) {
          wsInstance!.onopen = () => {
            resolve(true);
          };
        } else {
          setTimeout(resolve, 20);
        }
      });

      // Verify no WebSocket errors
      const wsErrors = consoleErrors.filter(error => 
        error.includes('WebSocket') || 
        error.includes('ws:')
      );
      expect(wsErrors).toHaveLength(0);
    });

    it('should handle WebSocket message sending', () => {
      const ws = new MockWebSocket('ws://localhost:5000/ws');
      
      expect(() => {
        ws.send(JSON.stringify({ type: 'test', data: 'hello' }));
      }).not.toThrow();
    });
  });

  describe('Error Handling and Console Validation', () => {
    it('should not produce console errors during normal operations', async () => {
      const mockJWT = createMockJWT();

      // Simulate successful API calls
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ user: mockUser, token: mockJWT })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUser
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockDishes
        });

      // Simulate API calls
      await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username: 'test', password: 'test' })
      });

      await fetch('/api/auth/profile', {
        headers: { 'Authorization': `Bearer ${mockJWT}` }
      });

      await fetch('/api/dishes');

      // Create WebSocket connection
      new MockWebSocket('ws://localhost:5000/ws');

      // Check that no relevant console errors were logged
      const relevantErrors = consoleErrors.filter(error => 
        !error.includes('Warning:') && // Ignore React warnings
        !error.includes('act(') && // Ignore act() warnings
        !error.includes('Cannot read properties of undefined')
      );
      
      expect(relevantErrors).toHaveLength(0);
    });

    it('should handle API errors gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ message: 'Unauthorized' })
      });

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username: 'wrong', password: 'wrong' })
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(401);

      const data = await response.json();
      expect(data.message).toBe('Unauthorized');
    });
  });

  describe('Authentication State Management', () => {
    it('should handle token storage and retrieval', () => {
      const mockJWT = createMockJWT();

      // Test that localStorage methods exist and can be called
      expect(typeof localStorage.setItem).toBe('function');
      expect(typeof localStorage.getItem).toBe('function');

      // Simulate storing token
      localStorage.setItem('authToken', mockJWT);

      // Test that getItem can be called
      const retrievedToken = localStorage.getItem('authToken');
      expect(typeof retrievedToken).toBeDefined();
    });

    it('should handle token removal on logout', () => {
      expect(typeof localStorage.removeItem).toBe('function');

      // Test that removeItem can be called without errors
      expect(() => {
        localStorage.removeItem('authToken');
      }).not.toThrow();
    });
  });
});
