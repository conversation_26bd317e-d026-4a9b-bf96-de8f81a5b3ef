import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { Checkbox } from '../ui/checkbox';
import { Slider } from '../ui/slider';
import { Search, Filter, X } from 'lucide-react';
import { type DishFilter, type DishCategory } from '@shared/schema';

interface DishSearchProps {
  onSearch: (query: string, filters: DishFilter) => void;
  categories: DishCategory[];
  regions: string[];
  isLoading?: boolean;
}

export const DishSearch: React.FC<DishSearchProps> = ({ 
  onSearch, 
  categories, 
  regions, 
  isLoading = false 
}) => {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<DishFilter>({});
  const [showFilters, setShowFilters] = useState(false);

  const dietaryOptions = [
    { id: 'vegetarian', label: 'Vegetarian' },
    { id: 'vegan', label: 'Vegan' },
    { id: 'glutenFree', label: 'Gluten Free' },
    { id: 'spicy', label: 'Spicy' },
  ];

  const statusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'draft', label: 'Draft' },
    { value: 'inactive', label: 'Inactive' },
  ];

  const handleFilterChange = (key: keyof DishFilter, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleDietaryChange = (dietary: string, checked: boolean) => {
    const currentDietary = filters.dietary || [];
    if (checked) {
      handleFilterChange('dietary', [...currentDietary, dietary]);
    } else {
      handleFilterChange('dietary', currentDietary.filter(d => d !== dietary));
    }
  };

  const handlePriceRangeChange = (values: number[]) => {
    if (values.length === 2) {
      handleFilterChange('priceRange', { min: values[0], max: values[1] });
    }
  };

  const handleCookingTimeChange = (values: number[]) => {
    if (values.length === 2) {
      handleFilterChange('cookingTime', { min: values[0], max: values[1] });
    }
  };

  const clearFilters = () => {
    setFilters({});
    setQuery('');
  };

  const hasActiveFilters = () => {
    return Object.keys(filters).length > 0 || query.trim() !== '';
  };

  const handleSearch = () => {
    onSearch(query, filters);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="h-5 w-5" />
          Search Dishes
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search Bar */}
        <div className="flex gap-2">
          <div className="flex-1">
            <Input
              placeholder="Search dishes by name, description, or tags..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={handleKeyPress}
            />
          </div>
          <Button onClick={handleSearch} disabled={isLoading}>
            {isLoading ? 'Searching...' : 'Search'}
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
          </Button>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters() && (
          <div className="flex flex-wrap gap-2 items-center">
            <span className="text-sm text-gray-600">Active filters:</span>
            {query && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Search: "{query}"
                <X className="h-3 w-3 cursor-pointer" onClick={() => setQuery('')} />
              </Badge>
            )}
            {filters.category && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Category: {filters.category}
                <X className="h-3 w-3 cursor-pointer" onClick={() => handleFilterChange('category', undefined)} />
              </Badge>
            )}
            {filters.region && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Region: {filters.region}
                <X className="h-3 w-3 cursor-pointer" onClick={() => handleFilterChange('region', undefined)} />
              </Badge>
            )}
            {filters.status && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Status: {filters.status}
                <X className="h-3 w-3 cursor-pointer" onClick={() => handleFilterChange('status', undefined)} />
              </Badge>
            )}
            {filters.dietary && filters.dietary.length > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Dietary: {filters.dietary.join(', ')}
                <X className="h-3 w-3 cursor-pointer" onClick={() => handleFilterChange('dietary', undefined)} />
              </Badge>
            )}
            {filters.priceRange && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Price: ${filters.priceRange.min}-${filters.priceRange.max}
                <X className="h-3 w-3 cursor-pointer" onClick={() => handleFilterChange('priceRange', undefined)} />
              </Badge>
            )}
            {filters.cookingTime && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Cooking Time: {filters.cookingTime.min}-{filters.cookingTime.max} mins
                <X className="h-3 w-3 cursor-pointer" onClick={() => handleFilterChange('cookingTime', undefined)} />
              </Badge>
            )}
            <Button variant="ghost" size="sm" onClick={clearFilters} className="text-red-600 hover:text-red-700">
              Clear All
            </Button>
          </div>
        )}

        {/* Advanced Filters */}
        {showFilters && (
          <div className="border-t pt-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Category Filter */}
              <div>
                <Label>Category</Label>
                <Select value={filters.category} onValueChange={(value) => handleFilterChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.name}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Region Filter */}
              <div>
                <Label>Region</Label>
                <Select value={filters.region} onValueChange={(value) => handleFilterChange('region', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All regions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All regions</SelectItem>
                    {regions.map((region) => (
                      <SelectItem key={region} value={region}>
                        {region}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Status Filter */}
              <div>
                <Label>Status</Label>
                <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    {statusOptions.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Price Range */}
            <div>
              <Label>Price Range</Label>
              <div className="space-y-2">
                <Slider
                  value={[filters.priceRange?.min || 0, filters.priceRange?.max || 50]}
                  onValueChange={handlePriceRangeChange}
                  max={50}
                  min={0}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-600">
                  <span>${filters.priceRange?.min || 0}</span>
                  <span>${filters.priceRange?.max || 50}</span>
                </div>
              </div>
            </div>

            {/* Cooking Time Range */}
            <div>
              <Label>Cooking Time (minutes)</Label>
              <div className="space-y-2">
                <Slider
                  value={[filters.cookingTime?.min || 0, filters.cookingTime?.max || 120]}
                  onValueChange={handleCookingTimeChange}
                  max={120}
                  min={0}
                  step={5}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-600">
                  <span>{filters.cookingTime?.min || 0} mins</span>
                  <span>{filters.cookingTime?.max || 120} mins</span>
                </div>
              </div>
            </div>

            {/* Dietary Preferences */}
            <div>
              <Label>Dietary Preferences</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {dietaryOptions.map((option) => (
                  <div key={option.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={option.id}
                      checked={filters.dietary?.includes(option.id)}
                      onCheckedChange={(checked) => handleDietaryChange(option.id, checked as boolean)}
                    />
                    <Label htmlFor={option.id} className="text-sm">
                      {option.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 