import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";

interface ProgressBarProps {
  progress: number;
  className?: string;
  variant?: "default" | "success" | "warning" | "voting";
  showPercentage?: boolean;
  animated?: boolean;
  size?: "sm" | "md" | "lg";
}

export default function ProgressBar({ 
  progress, 
  className, 
  variant = "default",
  showPercentage = false,
  animated = true,
  size = "md"
}: ProgressBarProps) {
  const [displayProgress, setDisplayProgress] = useState(0);

  // Animate progress changes
  useEffect(() => {
    if (animated) {
      const timer = setTimeout(() => {
        setDisplayProgress(progress);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setDisplayProgress(progress);
    }
  }, [progress, animated]);

  const getColorClass = () => {
    switch (variant) {
      case "success":
        return "bg-green-500";
      case "warning":
        return "bg-yellow-500";
      case "voting":
        return "bg-gradient-to-r from-green-400 to-green-600";
      default:
        return "bg-[var(--accent-green)]";
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case "sm":
        return "h-1";
      case "lg":
        return "h-4";
      default:
        return "h-2";
    }
  };

  const getContainerClass = () => {
    switch (size) {
      case "sm":
        return "rounded-full";
      case "lg":
        return "rounded-lg";
      default:
        return "rounded-md";
    }
  };

  return (
    <div className={cn("progress-bar", className)}>
      <div className={cn(
        "w-full bg-gray-200 overflow-hidden",
        getContainerClass(),
        getSizeClass()
      )}>
        <div 
          className={cn(
            "progress-fill transition-all duration-700 ease-out",
            getColorClass(),
            getSizeClass()
          )}
          style={{ 
            width: `${Math.min(displayProgress, 100)}%`,
            transition: animated ? 'width 700ms ease-out' : 'none'
          }}
        />
      </div>
      {showPercentage && (
        <div className="text-xs text-gray-600 mt-1 text-right">
          {Math.round(displayProgress)}%
        </div>
      )}
    </div>
  );
}
