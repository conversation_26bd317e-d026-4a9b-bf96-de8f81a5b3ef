import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Plus, ChartLine, Star, Clock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import AddDishModal from "./add-dish-modal";
import ProgressBar from "./progress-bar";
import ChefOrderManagement from "./chef-order-management";
import { useWebSocket } from "@/hooks/use-websocket";
import type { Dish, User } from "@shared/schema";

// Mock chef data - in real app this would come from auth context
const mockChef: User = {
  id: 1,
  username: "oma_m",
  name: "<PERSON><PERSON>",
  email: "<EMAIL>",
  password: "",
  role: "chef",
  region: "Manipur",
  rating: "4.9",
  totalOrders: 127,
  isActive: true,
  avatar: "https://images.unsplash.com/photo-1583394838336-acd977736f90?ixlib=rb-4.0.3&w=80&h=80&fit=crop&crop=face"
};

export default function ChefView() {
  const [showAddDish, setShowAddDish] = useState(false);

  // Real-time WebSocket connection
  useWebSocket();

  // Fallback mock dishes for chef
  const mockDishes: Dish[] = [
    {
      id: 1,
      name: "Signature Biryani",
      description: "My special recipe passed down through generations",
      price: 299,
      image: "https://images.unsplash.com/photo-1563379091339-03246963d96c?w=400",
      region: "Hyderabad",
      cookingTime: 45,
      minOrders: 10,
      category: "Main Course",
      currentVotes: 7,
      isActive: true,
      status: "active",
      tags: ["signature", "traditional"],
      ingredients: ["basmati rice", "mutton", "saffron"],
      allergens: [],
      dietaryInfo: ["non-vegetarian"],
      chefId: mockChef.id,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // Fetch chef's dishes
  const { data: fetchedDishes = [], isLoading } = useQuery<Dish[]>({
    queryKey: [`/api/chefs/${mockChef.id}/dishes`],
  });

  // Use fetched data if available, otherwise use mock data
  const dishes = fetchedDishes.length > 0 ? fetchedDishes : mockDishes;

  // Calculate stats
  const activeDishes = dishes.filter(dish => dish.isActive).length;
  const monthlyEarnings = dishes.reduce((sum, dish) => {
    return sum + (parseFloat(dish.price) * (dish.currentVotes || 0));
  }, 0);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Chef Dashboard Header */}
      <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)] mb-8">
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <img
              src={mockChef.avatar || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&w=100&h=100&fit=crop&crop=face"}
              alt={mockChef.name}
              className="w-16 h-16 rounded-full object-cover"
            />
            <div>
              <h2 className="text-2xl font-bold">Welcome back, {mockChef.name}</h2>
              <p className="text-[var(--text-secondary)]">{mockChef.region} Regional Specialist</p>
              <div className="flex items-center mt-2">
                <div className="flex text-yellow-400 text-sm mr-2">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-current" />
                  ))}
                </div>
                <span className="text-sm">{mockChef.rating} ({mockChef.totalOrders} reviews)</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
        <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-[var(--accent-green)] mb-2">{activeDishes}</div>
            <div className="text-[var(--text-secondary)]">Active Dishes</div>
          </CardContent>
        </Card>
        <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-[var(--accent-green)] mb-2">
              ${monthlyEarnings.toFixed(0)}
            </div>
            <div className="text-[var(--text-secondary)]">This Month</div>
          </CardContent>
        </Card>
        <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-[var(--accent-green)] mb-2">{mockChef.totalOrders}</div>
            <div className="text-[var(--text-secondary)]">Total Orders</div>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-4 mb-8">
        <Button
          onClick={() => setShowAddDish(true)}
          className="btn-primary"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add New Dish
        </Button>
        <Button variant="outline" className="btn-secondary">
          <ChartLine className="w-4 h-4 mr-2" />
          View Analytics
        </Button>
      </div>

      {/* Current Dishes */}
      <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
        <CardContent className="p-6">
          <h3 className="text-xl font-bold mb-6">Your Dishes</h3>
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse border border-[var(--border-gray)] rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gray-600 rounded-lg"></div>
                      <div>
                        <div className="h-4 bg-gray-600 rounded mb-2 w-24"></div>
                        <div className="h-3 bg-gray-600 rounded w-32"></div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="h-4 bg-gray-600 rounded mb-1 w-16"></div>
                      <div className="h-3 bg-gray-600 rounded w-20"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {dishes.map((dish) => {
                const currentVotes = dish.currentVotes || 0;
                const progress = (currentVotes / dish.minOrders) * 100;
                const isReady = currentVotes >= dish.minOrders;

                return (
                  <div
                    key={dish.id}
                    className={`border rounded-lg p-4 ${
                      isReady
                        ? 'border-yellow-500 bg-yellow-500 bg-opacity-10'
                        : 'border-[var(--border-gray)]'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <img
                          src={dish.image}
                          alt={dish.name}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                        <div>
                          <h4 className="font-semibold">{dish.name}</h4>
                          <p className="text-[var(--text-secondary)] text-sm">
                            ${dish.price} • Min: {dish.minOrders} orders
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`font-semibold ${
                          isReady ? 'text-yellow-500' : 'text-[var(--accent-green)]'
                        }`}>
                          {currentVotes}/{dish.minOrders} votes
                        </div>
                        <div className={`text-sm ${
                          isReady ? 'text-yellow-500' : 'text-[var(--text-secondary)]'
                        }`}>
                          {isReady ? 'Ready to cook!' : `${Math.round(progress)}% to threshold`}
                        </div>
                      </div>
                    </div>
                    <div className="mt-3">
                      <ProgressBar 
                        progress={progress}
                        className="mb-2"
                      />
                      <div className="flex justify-between">
                        {isReady ? (
                          <Button className="w-full bg-yellow-500 text-white hover:bg-yellow-600">
                            Start Cooking
                          </Button>
                        ) : (
                          <>
                            <button className="text-[var(--accent-green)] text-sm hover:underline">
                              Edit Dish
                            </button>
                            <button className="text-[var(--text-secondary)] text-sm hover:underline">
                              View Details
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Order Management */}
      <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
        <CardContent className="p-6">
          <h3 className="text-xl font-bold mb-6">Order Management</h3>
          <ChefOrderManagement chefId={mockChef.id} />
        </CardContent>
      </Card>

      {/* Add Dish Modal */}
      {showAddDish && (
        <AddDishModal
          chefId={mockChef.id}
          onClose={() => setShowAddDish(false)}
        />
      )}
    </div>
  );
}
