# CloudKitchen Deployment Guide 🚀

This guide will help you deploy the CloudKitchen application to various cloud platforms.

## Prerequisites

Before deploying, ensure you have:

1. **PostgreSQL Database**: Set up a PostgreSQL database (local or cloud)
2. **Environment Variables**: Configure the required environment variables
3. **Git Repository**: Push your code to a Git repository (GitHub, GitLab, etc.)

## Required Environment Variables

Create a `.env` file or set these environment variables in your deployment platform:

```bash
DATABASE_URL=postgresql://username:password@host:port/database
JWT_SECRET=your-super-secret-jwt-key-here
SESSION_SECRET=your-super-secret-session-key-here
NODE_ENV=production
```

### Generating Secrets

You can generate secure secrets using:

```bash
# Generate JWT secret
openssl rand -base64 32

# Generate session secret
openssl rand -base64 32
```

## Deployment Options

### 1. Railway (Recommended) 🚂

Railway is a modern platform that makes deployment simple.

#### Quick Deploy
1. Install Railway CLI: `npm install -g @railway/cli`
2. Login: `railway login`
3. Deploy: `./deploy.sh railway`

#### Manual Deploy
1. Go to [Railway](https://railway.app)
2. Connect your GitHub repository
3. Add environment variables in the Railway dashboard
4. Deploy automatically on push

### 2. Render 🎨

Render provides free hosting with automatic deployments.

#### Quick Deploy
1. Go to [Render](https://render.com)
2. Create a new Web Service
3. Connect your GitHub repository
4. Render will automatically detect the `render.yaml` file
5. Add environment variables in the Render dashboard

### 3. Vercel ⚡

Vercel is great for full-stack applications.

#### Quick Deploy
1. Install Vercel CLI: `npm install -g vercel`
2. Deploy: `./deploy.sh vercel`

#### Manual Deploy
1. Go to [Vercel](https://vercel.com)
2. Import your GitHub repository
3. Vercel will automatically detect the `vercel.json` configuration
4. Add environment variables in the Vercel dashboard

### 4. Docker 🐳

For local deployment or custom hosting.

#### Quick Deploy
```bash
./deploy.sh docker
```

#### Manual Deploy
```bash
# Build the image
docker build -t cloudkitchen .

# Run the container
docker run -d \
  --name cloudkitchen \
  -p 5000:5000 \
  -e DATABASE_URL="your-database-url" \
  -e JWT_SECRET="your-jwt-secret" \
  -e SESSION_SECRET="your-session-secret" \
  -e NODE_ENV=production \
  cloudkitchen
```

### 5. Docker Compose 🐳

For local development with database included.

```bash
# Set environment variables
export DATABASE_URL="postgresql://postgres:password@localhost:5432/cloudkitchen"
export JWT_SECRET="your-jwt-secret"
export SESSION_SECRET="your-session-secret"

# Start services
docker-compose up -d
```

## Database Setup

### Option 1: Cloud Database (Recommended)

Use a managed PostgreSQL service:

- **Railway**: Built-in PostgreSQL database
- **Render**: Built-in PostgreSQL database
- **Supabase**: Free PostgreSQL hosting
- **Neon**: Serverless PostgreSQL
- **PlanetScale**: MySQL (requires schema changes)

### Option 2: Local Database

For development or testing:

```bash
# Using Docker
docker run -d \
  --name postgres \
  -e POSTGRES_DB=cloudkitchen \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  postgres:15-alpine

# Run migrations
npm run db:push
```

## Post-Deployment

After deployment:

1. **Verify Health Check**: Visit `/api/dishes` to ensure the API is working
2. **Test Authentication**: Try registering and logging in
3. **Check WebSocket**: Verify real-time features work
4. **Monitor Logs**: Check application logs for any errors

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Verify `DATABASE_URL` is correct
   - Ensure database is accessible from your deployment platform
   - Check firewall settings

2. **Build Failures**
   - Ensure all dependencies are in `package.json`
   - Check for TypeScript compilation errors
   - Verify Node.js version compatibility

3. **Environment Variables Missing**
   - Double-check all required variables are set
   - Ensure no typos in variable names
   - Restart the application after adding variables

4. **WebSocket Issues**
   - Verify WebSocket endpoint is accessible
   - Check if your platform supports WebSockets
   - Ensure proper routing configuration

### Getting Help

- Check the application logs in your deployment platform
- Review the server logs for error messages
- Test locally first using `npm run dev`

## Security Considerations

1. **Use HTTPS**: All production deployments should use HTTPS
2. **Secure Secrets**: Never commit secrets to version control
3. **Database Security**: Use strong passwords and restrict access
4. **Rate Limiting**: Consider implementing rate limiting for production
5. **CORS**: Configure CORS properly for your domain

## Performance Optimization

1. **Database Indexing**: Add indexes for frequently queried fields
2. **Caching**: Implement Redis for session storage and caching
3. **CDN**: Use a CDN for static assets
4. **Compression**: Enable gzip compression
5. **Monitoring**: Set up application monitoring and alerting

---

Happy deploying! 🎉 