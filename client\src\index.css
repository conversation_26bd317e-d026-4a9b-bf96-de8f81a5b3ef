@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(155, 84%, 44%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;

  /* Custom CloudKitchen colors */
  --primary-dark: hsl(0, 0%, 10%);
  --secondary-dark: hsl(0, 0%, 18%);
  --accent-green: hsl(155, 84%, 44%);
  --text-secondary: hsl(0, 0%, 64%);
  --border-gray: hsl(220, 13%, 26%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(155, 84%, 44%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    background-color: var(--primary-dark);
    color: white;
    font-family: 'Inter', sans-serif;
  }

  html {
    font-family: 'Inter', sans-serif;
  }
}

@layer components {
  .nav-tab {
    @apply px-3 py-2 rounded-md text-sm font-medium transition-all duration-300;
    color: var(--text-secondary);
  }
  
  .nav-tab.active {
    color: var(--accent-green);
    background-color: rgba(16, 185, 129, 0.1);
  }
  
  .nav-tab:hover {
    color: var(--accent-green);
  }

  .dish-card, .chef-card {
    @apply transition-all duration-300;
  }
  
  .dish-card:hover, .chef-card:hover {
    @apply transform -translate-y-1;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  }

  .progress-bar {
    @apply w-full rounded-full h-2;
    background-color: hsl(0, 0%, 44%);
  }

  .progress-fill {
    @apply h-2 rounded-full transition-all duration-500;
    background-color: var(--accent-green);
  }
}

/* Scrollbar styling for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-green);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(155, 84%, 38%);
}

/* Loading animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom button styles */
.btn-primary {
  background-color: var(--accent-green);
  color: white;
  @apply font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-primary:hover {
  background-color: hsl(155, 84%, 38%);
}

.btn-secondary {
  background-color: var(--secondary-dark);
  border: 1px solid var(--border-gray);
  color: white;
  @apply font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary:hover {
  background-color: hsl(0, 0%, 25%);
}
