import { Star } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import type { User } from "@shared/schema";

interface ChefCardProps {
  chef: User;
  onClick?: () => void;
}

export default function ChefCard({ chef, onClick }: ChefCardProps) {
  const rating = chef.rating ? parseFloat(chef.rating) : 0;
  
  return (
    <Card 
      className="chef-card bg-[var(--secondary-dark)] border-[var(--border-gray)] cursor-pointer"
      onClick={onClick}
    >
      <CardContent className="p-4 text-center">
        <img
          src={chef.avatar || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&w=100&h=100&fit=crop&crop=face"}
          alt={chef.name}
          className="w-16 h-16 rounded-full mx-auto mb-3 object-cover"
        />
        <h4 className="font-semibold text-sm mb-1">{chef.name}</h4>
        <div className="flex items-center justify-center mb-1">
          <div className="flex text-yellow-400 text-xs">
            {[...Array(5)].map((_, i) => (
              <Star 
                key={i} 
                className={`w-3 h-3 ${
                  i < Math.floor(rating) 
                    ? 'fill-current' 
                    : 'opacity-30'
                }`} 
              />
            ))}
          </div>
          <span className="text-[var(--text-secondary)] text-xs ml-1">
            {chef.rating || '0'}
          </span>
        </div>
        <p className="text-[var(--text-secondary)] text-xs">{chef.region}</p>
        <p className="text-[var(--text-secondary)] text-xs mt-1">
          {chef.totalOrders} orders
        </p>
      </CardContent>
    </Card>
  );
}
