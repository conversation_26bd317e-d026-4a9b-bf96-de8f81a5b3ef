version: '3.8'

services:
  app:
    build: .
    ports:
      - "0.0.0.0:5000:5000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************/cloudkitchen
      - JWT_SECRET=your-super-secret-jwt-key-here-change-in-production
      - SESSION_SECRET=your-super-secret-session-key-here-change-in-production
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=cloudkitchen
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data: 