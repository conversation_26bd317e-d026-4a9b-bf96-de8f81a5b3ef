services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************/cloudkitchen
      - JWT_SECRET=your-super-secret-jwt-key-here-change-in-production
      - SESSION_SECRET=your-super-secret-session-key-here-change-in-production
      - PORT=5000
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - cloudkitchen-network

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=cloudkitchen
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cloudkitchen-network

networks:
  cloudkitchen-network:
    driver: bridge

volumes:
  postgres_data: