import { Clock, MapPin, Truck } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export default function DeliveryTracking() {
  // Mock order data - in real app this would come from API
  const activeOrder = {
    id: "CK-2025-001",
    dishName: "Iromba",
    chefName: "Oma M.",
    status: "cooking",
    image: "https://images.unsplash.com/photo-1603133872878-684f208fb84b?ixlib=rb-4.0.3&w=60&h=60&fit=crop",
    estimatedDelivery: "35 min",
    trackingSteps: [
      { label: "Threshold Met", completed: true, time: "10 min ago" },
      { label: "Cooking Started", completed: true, time: "5 min ago" },
      { label: "Preparing for Delivery", completed: false, time: "ETA: 35 min" },
      { label: "Out for Delivery", completed: false, time: "" },
      { label: "Delivered", completed: false, time: "" }
    ]
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "cooking":
        return "bg-yellow-500";
      case "ready":
        return "bg-blue-500";
      case "delivering":
        return "bg-purple-500";
      case "completed":
        return "bg-[var(--accent-green)]";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Order Tracking Card */}
      <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold">Order #{activeOrder.id}</h4>
            <Badge className={getStatusColor(activeOrder.status)}>
              {activeOrder.status.charAt(0).toUpperCase() + activeOrder.status.slice(1)}
            </Badge>
          </div>
          
          <div className="flex items-center space-x-4 mb-4">
            <img
              src={activeOrder.image}
              alt={activeOrder.dishName}
              className="w-12 h-12 rounded-lg object-cover"
            />
            <div>
              <p className="font-medium">{activeOrder.dishName}</p>
              <p className="text-[var(--text-secondary)] text-sm">by {activeOrder.chefName}</p>
            </div>
          </div>

          <div className="space-y-3 mb-4">
            {activeOrder.trackingSteps.map((step, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${
                    step.completed 
                      ? 'bg-[var(--accent-green)]' 
                      : index === 2 
                        ? 'bg-gray-600 animate-pulse' 
                        : 'bg-gray-600'
                  }`}></div>
                  <span className="text-sm">{step.label}</span>
                </div>
                <span className="text-[var(--text-secondary)] text-sm">{step.time}</span>
              </div>
            ))}
          </div>

          <Button className="w-full btn-primary">
            <MapPin className="w-4 h-4 mr-2" />
            Track Delivery
          </Button>
        </CardContent>
      </Card>

      {/* Map Placeholder */}
      <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
        <CardContent className="p-6">
          <h4 className="font-semibold mb-4">Delivery Tracking</h4>
          <div className="relative bg-gray-800 rounded-lg h-48 flex items-center justify-center overflow-hidden">
            <img
              src="https://images.unsplash.com/photo-1569336415962-a4bd9f69cd83?ixlib=rb-4.0.3&w=400&h=200&fit=crop"
              alt="Delivery tracking map"
              className="w-full h-full object-cover opacity-75"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-black bg-opacity-70 text-white px-4 py-2 rounded-lg flex items-center">
                <Truck className="w-4 h-4 mr-2" />
                Driver en route - 25 min
              </div>
            </div>
            
            {/* Simulated route markers */}
            <div className="absolute top-4 left-4 w-3 h-3 bg-[var(--accent-green)] rounded-full animate-pulse"></div>
            <div className="absolute bottom-6 right-6 w-3 h-3 bg-blue-500 rounded-full"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-yellow-500 rounded-full animate-bounce"></div>
          </div>
          
          <div className="mt-4 flex items-center justify-between text-sm">
            <div className="flex items-center text-[var(--accent-green)]">
              <div className="w-2 h-2 bg-[var(--accent-green)] rounded-full mr-2"></div>
              Chef Location
            </div>
            <div className="flex items-center text-blue-500">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
              Your Location
            </div>
            <div className="flex items-center text-yellow-500">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
              Driver
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
