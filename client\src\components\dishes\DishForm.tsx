import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { Checkbox } from '../ui/checkbox';
import { type Dish, type InsertDish, type DishCategory } from '@shared/schema';

interface DishFormProps {
  dish?: Dish;
  categories: DishCategory[];
  onSubmit: (dish: InsertDish) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

type DietaryKey = 'vegetarian' | 'vegan' | 'glutenFree' | 'spicy';

export const DishForm: React.FC<DishFormProps> = ({ 
  dish, 
  categories, 
  onSubmit, 
  onCancel, 
  isLoading = false 
}) => {
  const [formData, setFormData] = useState<Partial<InsertDish>>({
    name: dish?.name || '',
    description: dish?.description || '',
    price: dish?.price || '',
    image: dish?.image || '',
    region: dish?.region || '',
    cookingTime: dish?.cookingTime || 30,
    minOrders: dish?.minOrders || 20,
    category: dish?.category || '',
    status: dish?.status || 'draft',
    tags: dish?.tags || [],
    ingredients: dish?.ingredients || [],
    allergens: dish?.allergens || [],
    dietaryInfo: dish?.dietaryInfo || {
      vegetarian: false,
      vegan: false,
      glutenFree: false,
      spicy: false,
    },
  });

  const [newTag, setNewTag] = useState('');
  const [newIngredient, setNewIngredient] = useState('');
  const [newAllergen, setNewAllergen] = useState('');

  const regions = [
    'Manipur', 'Nagaland', 'Arunachal Pradesh', 'Bihar', 'Assam', 
    'Meghalaya', 'Mizoram', 'Tripura', 'Sikkim', 'Sydney CBD'
  ];

  const handleInputChange = (field: keyof InsertDish, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleDietaryChange = (field: DietaryKey, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      dietaryInfo: {
        ...prev.dietaryInfo!,
        [field]: checked,
      },
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()],
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || [],
    }));
  };

  const addIngredient = () => {
    if (newIngredient.trim() && !formData.ingredients?.includes(newIngredient.trim())) {
      setFormData(prev => ({
        ...prev,
        ingredients: [...(prev.ingredients || []), newIngredient.trim()],
      }));
      setNewIngredient('');
    }
  };

  const removeIngredient = (ingredientToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      ingredients: prev.ingredients?.filter(ingredient => ingredient !== ingredientToRemove) || [],
    }));
  };

  const addAllergen = () => {
    if (newAllergen.trim() && !formData.allergens?.includes(newAllergen.trim())) {
      setFormData(prev => ({
        ...prev,
        allergens: [...(prev.allergens || []), newAllergen.trim()],
      }));
      setNewAllergen('');
    }
  };

  const removeAllergen = (allergenToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      allergens: prev.allergens?.filter(allergen => allergen !== allergenToRemove) || [],
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.name && formData.description && formData.price && formData.category) {
      await onSubmit(formData as InsertDish);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{dish ? 'Edit Dish' : 'Create New Dish'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Dish Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter dish name"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="category">Category *</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.name}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe your dish..."
                rows={3}
                required
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="price">Price *</Label>
                <Input
                  id="price"
                  type="number"
                  step="0.01"
                  value={formData.price}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  placeholder="0.00"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="cookingTime">Cooking Time (mins)</Label>
                <Input
                  id="cookingTime"
                  type="number"
                  value={formData.cookingTime}
                  onChange={(e) => handleInputChange('cookingTime', parseInt(e.target.value))}
                  placeholder="30"
                />
              </div>
              
              <div>
                <Label htmlFor="minOrders">Min Orders</Label>
                <Input
                  id="minOrders"
                  type="number"
                  value={formData.minOrders}
                  onChange={(e) => handleInputChange('minOrders', parseInt(e.target.value))}
                  placeholder="20"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="region">Region</Label>
                <Select value={formData.region} onValueChange={(value) => handleInputChange('region', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    {regions.map((region) => (
                      <SelectItem key={region} value={region}>
                        {region}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="image">Image URL</Label>
              <Input
                id="image"
                value={formData.image}
                onChange={(e) => handleInputChange('image', e.target.value)}
                placeholder="https://example.com/image.jpg"
              />
            </div>
          </div>

          {/* Tags */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Tags</h3>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
              />
              <Button type="button" onClick={addTag} variant="outline">
                Add
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.tags?.map((tag, index) => (
                <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                  {tag} ×
                </Badge>
              ))}
            </div>
          </div>

          {/* Ingredients */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Ingredients</h3>
            <div className="flex gap-2">
              <Input
                value={newIngredient}
                onChange={(e) => setNewIngredient(e.target.value)}
                placeholder="Add an ingredient"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addIngredient())}
              />
              <Button type="button" onClick={addIngredient} variant="outline">
                Add
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.ingredients?.map((ingredient, index) => (
                <Badge key={index} variant="outline" className="cursor-pointer" onClick={() => removeIngredient(ingredient)}>
                  {ingredient} ×
                </Badge>
              ))}
            </div>
          </div>

          {/* Allergens */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Allergens</h3>
            <div className="flex gap-2">
              <Input
                value={newAllergen}
                onChange={(e) => setNewAllergen(e.target.value)}
                placeholder="Add an allergen"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAllergen())}
              />
              <Button type="button" onClick={addAllergen} variant="outline">
                Add
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.allergens?.map((allergen, index) => (
                <Badge key={index} variant="destructive" className="cursor-pointer" onClick={() => removeAllergen(allergen)}>
                  {allergen} ×
                </Badge>
              ))}
            </div>
          </div>

          {/* Dietary Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Dietary Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="vegetarian"
                  checked={formData.dietaryInfo?.vegetarian}
                  onCheckedChange={(checked) => handleDietaryChange('vegetarian', checked as boolean)}
                />
                <Label htmlFor="vegetarian">Vegetarian</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="vegan"
                  checked={formData.dietaryInfo?.vegan}
                  onCheckedChange={(checked) => handleDietaryChange('vegan', checked as boolean)}
                />
                <Label htmlFor="vegan">Vegan</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="glutenFree"
                  checked={formData.dietaryInfo?.glutenFree}
                  onCheckedChange={(checked) => handleDietaryChange('glutenFree', checked as boolean)}
                />
                <Label htmlFor="glutenFree">Gluten Free</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="spicy"
                  checked={formData.dietaryInfo?.spicy}
                  onCheckedChange={(checked) => handleDietaryChange('spicy', checked as boolean)}
                />
                <Label htmlFor="spicy">Spicy</Label>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : (dish ? 'Update Dish' : 'Create Dish')}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}; 