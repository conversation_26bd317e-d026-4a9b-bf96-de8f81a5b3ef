import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { DishCard } from './DishCard';
import { type DishWithChef } from '@shared/schema';

const mockDish: DishWithChef = {
  id: 1,
  chefId: 1,
  name: 'Test Dish',
  description: 'A delicious test dish',
  price: '15.99',
  image: 'test.jpg',
  region: 'Test Region',
  cookingTime: 30,
  minOrders: 5,
  currentVotes: 3,
  isActive: true,
  category: 'Main Course',
  tags: ['spicy', 'popular'],
  status: 'active',
  ingredients: ['ingredient1', 'ingredient2'],
  allergens: ['nuts'],
  dietaryInfo: {
    vegetarian: false,
    vegan: false,
    glutenFree: true,
    spicy: true
  },
  createdAt: new Date(),
  updatedAt: new Date(),
  chef: {
    id: 1,
    username: 'testchef',
    name: 'Test Chef',
    email: '<EMAIL>',
    password: 'password',
    role: 'chef',
    region: 'Test Region',
    rating: '4.5',
    totalOrders: 50,
    isActive: true,
    avatar: 'chef.jpg'
  },
  voteProgress: 60,
  canStartCooking: false
};

describe('DishCard', () => {
  const mockOnVote = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders dish information correctly', () => {
    render(<DishCard dish={mockDish} onVote={mockOnVote} />);

    expect(screen.getByText(mockDish.name)).toBeInTheDocument();
    expect(screen.getByText(mockDish.description)).toBeInTheDocument();
    expect(screen.getByText(`$${mockDish.price}`)).toBeInTheDocument();
    expect(screen.getByText(`${mockDish.cookingTime} mins`)).toBeInTheDocument();
    expect(screen.getByText(`${mockDish.currentVotes}/${mockDish.minOrders} votes`)).toBeInTheDocument();
  });

  it('displays chef information', () => {
    render(<DishCard dish={mockDish} onVote={mockOnVote} />);

    expect(screen.getByText(mockDish.chef.name)).toBeInTheDocument();
    expect(screen.getByText(`⭐ ${mockDish.chef.rating}`)).toBeInTheDocument();
  });

  it('shows vote progress bar', () => {
    render(<DishCard dish={mockDish} onVote={mockOnVote} />);

    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toBeInTheDocument();
    expect(progressBar).toHaveAttribute('aria-valuenow', '60');
  });

  it('calls onVote when vote button is clicked', async () => {
    render(<DishCard dish={mockDish} onVote={mockOnVote} />);

    const voteButton = screen.getByRole('button', { name: /vote/i });
    await userEvent.click(voteButton);

    expect(mockOnVote).toHaveBeenCalledWith(mockDish.id);
  });

  it('disables vote button when canStartCooking is true', () => {
    const dishWithCooking = { ...mockDish, canStartCooking: true };
    render(<DishCard dish={dishWithCooking} onVote={mockOnVote} />);

    const voteButton = screen.getByRole('button');
    expect(voteButton).toBeDisabled();
  });
}); 