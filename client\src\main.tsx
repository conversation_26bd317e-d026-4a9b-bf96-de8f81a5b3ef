import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

console.log('main.tsx: Starting React app...');

// Set document title immediately
document.title = 'CloudKitchen - Authentic Regional Flavors';

// Add fallback content immediately
const rootElement = document.getElementById("root");
if (rootElement) {
  console.log('main.tsx: Found root element');

  // Add immediate fallback content
  rootElement.innerHTML = `
    <div style="min-height: 100vh; background: #1a1a1a; color: white; padding: 20px;">
      <h1 style="color: #10b981; font-size: 24px; margin-bottom: 16px;">CloudKitchen</h1>
      <h2 style="font-size: 20px; margin-bottom: 12px;">Authentic Regional Flavors from Home Chefs</h2>
      <p style="color: #9ca3af; margin-bottom: 16px;">Discover amazing dishes from talented chefs across India. Vote for rare dishes and experience true flavors.</p>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px; margin-top: 24px;">
        <div style="background: #374151; padding: 16px; border-radius: 8px;">
          <h3 style="font-size: 16px; margin-bottom: 8px;">Authentic Hyderabadi Biryani</h3>
          <p style="color: #9ca3af; margin-bottom: 12px;">Traditional aromatic rice dish with tender mutton and exotic spices</p>
          <div style="display: flex; justify-content: space-between;">
            <span style="color: #10b981; font-weight: bold;">₹299</span>
            <span style="color: #6b7280; font-size: 14px;">45 min</span>
          </div>
        </div>
        <div style="background: #374151; padding: 16px; border-radius: 8px;">
          <h3 style="font-size: 16px; margin-bottom: 8px;">Manipuri Iromba</h3>
          <p style="color: #9ca3af; margin-bottom: 12px;">Traditional fermented fish curry with fresh vegetables</p>
          <div style="display: flex; justify-content: space-between;">
            <span style="color: #10b981; font-weight: bold;">₹199</span>
            <span style="color: #6b7280; font-size: 14px;">30 min</span>
          </div>
        </div>
      </div>
      <div style="margin-top: 32px; text-align: center;">
        <p style="color: #6b7280;">🍽️ Authentic Dishes • 👨‍🍳 Home Chefs • ⭐ Vote & Review</p>
      </div>
    </div>
  `;

  // Try to render React app
  try {
    console.log('main.tsx: Attempting to render React app...');
    createRoot(rootElement).render(<App />);
    console.log('main.tsx: React app rendered successfully');
  } catch (error) {
    console.error('main.tsx: Error rendering React app:', error);
  }
} else {
  console.error('main.tsx: Root element not found!');
}
