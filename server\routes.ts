import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { storage } from "./storage";
import { insertDishSchema, insertVoteSchema, insertOrderSchema } from "../shared/schema";
import authRoutes from "./routes/auth";
import dishesRoutes from "./routes/dishes";

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);

  // WebSocket server for real-time updates
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });

  // Store connected clients
  const clients = new Set<WebSocket>();

  wss.on('connection', (ws) => {
    clients.add(ws);
    console.log('WebSocket client connected');

    ws.on('close', () => {
      clients.delete(ws);
      console.log('WebSocket client disconnected');
    });

    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
      clients.delete(ws);
    });
  });

  // Broadcast function for real-time updates
  function broadcast(data: any) {
    const message = JSON.stringify(data);
    clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  // API Routes

  // Authentication routes
  app.use("/api/auth", authRoutes);

  // Enhanced dish management routes
  app.use("/api/dishes", dishesRoutes);

  // Get all dishes with chef information
  app.get("/api/dishes", async (req, res) => {
    try {
      const dishes = await storage.getDishesWithChef();
      res.json(dishes);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch dishes" });
    }
  });

  // Get dish by ID
  app.get("/api/dishes/:id", async (req, res) => {
    try {
      const dishId = parseInt(req.params.id);
      const dish = await storage.getDish(dishId);
      if (!dish) {
        return res.status(404).json({ message: "Dish not found" });
      }
      const chef = await storage.getUser(dish.chefId);
      res.json({ ...dish, chef });
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch dish" });
    }
  });

  // Create new dish (chef only)
  app.post("/api/dishes", async (req, res) => {
    try {
      const dishData = insertDishSchema.parse(req.body);
      const dish = await storage.createDish(dishData);
      
      // Broadcast new dish to all clients
      broadcast({ type: 'dishCreated', dish });
      
      res.status(201).json(dish);
    } catch (error) {
      res.status(400).json({ message: "Invalid dish data" });
    }
  });

  // Get dishes by chef
  app.get("/api/chefs/:chefId/dishes", async (req, res) => {
    try {
      const chefId = parseInt(req.params.chefId);
      const dishes = await storage.getDishesByChef(chefId);
      res.json(dishes);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch chef dishes" });
    }
  });

  // Vote for a dish
  app.post("/api/votes", async (req, res) => {
    try {
      const voteData = insertVoteSchema.parse(req.body);
      
      // Check if user already voted
      const hasVoted = await storage.hasUserVoted(voteData.userId, voteData.dishId);
      if (hasVoted) {
        return res.status(400).json({ message: "User has already voted for this dish" });
      }

      const vote = await storage.createVote(voteData);
      const dish = await storage.getDish(voteData.dishId);
      
      if (dish) {
        const progress = ((dish.currentVotes || 0) / dish.minOrders) * 100;
        const canStartCooking = (dish.currentVotes || 0) >= dish.minOrders;
        
        // Broadcast vote update to all clients
        broadcast({
          type: 'voteUpdate',
          dishId: dish.id,
          currentVotes: dish.currentVotes || 0,
          progress: progress,
          canStartCooking: canStartCooking,
          votesNeeded: Math.max(0, dish.minOrders - (dish.currentVotes || 0)),
          thresholdReached: canStartCooking && (dish.currentVotes || 0) === dish.minOrders
        });

        // If threshold was just reached, broadcast order creation
        if (canStartCooking && (dish.currentVotes || 0) === dish.minOrders) {
          const orders = await storage.getOrdersByDish(dish.id);
          broadcast({
            type: 'thresholdReached',
            dishId: dish.id,
            dishName: dish.name,
            chefId: dish.chefId,
            orders: orders,
            message: `Threshold reached for ${dish.name}! Cooking will begin soon.`
          });
        }
      }
      
      res.status(201).json(vote);
    } catch (error) {
      res.status(400).json({ message: "Invalid vote data" });
    }
  });

  // Get featured chefs (top rated)
  app.get("/api/chefs/featured", async (req, res) => {
    try {
      const chefs = await storage.getUsersByRole("chef");
      const featuredChefs = chefs
        .filter(chef => parseFloat(chef.rating || "0") >= 4.7)
        .sort((a, b) => parseFloat(b.rating || "0") - parseFloat(a.rating || "0"))
        .slice(0, 10);
      res.json(featuredChefs);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch featured chefs" });
    }
  });

  // Get all chefs (admin)
  app.get("/api/chefs", async (req, res) => {
    try {
      const chefs = await storage.getUsersByRole("chef");
      res.json(chefs);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch chefs" });
    }
  });

  // Create order when dish reaches threshold
  app.post("/api/orders", async (req, res) => {
    try {
      const orderData = insertOrderSchema.parse(req.body);
      const order = await storage.createOrder(orderData);
      
      // Broadcast order creation
      broadcast({ type: 'orderCreated', order });
      
      res.status(201).json(order);
    } catch (error) {
      res.status(400).json({ message: "Invalid order data" });
    }
  });

  // Get orders by user
  app.get("/api/users/:userId/orders", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const orders = await storage.getOrdersByUser(userId);
      res.json(orders);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch user orders" });
    }
  });

  // Get orders by chef
  app.get("/api/chefs/:chefId/orders", async (req, res) => {
    try {
      const chefId = parseInt(req.params.chefId);
      const orders = await storage.getOrdersByChef(chefId);
      res.json(orders);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch chef orders" });
    }
  });

  // Update order status
  app.patch("/api/orders/:orderId/status", async (req, res) => {
    try {
      const orderId = parseInt(req.params.orderId);
      const { status } = req.body;
      
      await storage.updateOrderStatus(orderId, status);
      
      // Broadcast status update
      broadcast({
        type: 'orderStatusUpdate',
        orderId,
        status
      });
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to update order status" });
    }
  });

  // Get all orders with details (admin)
  app.get("/api/orders", async (req, res) => {
    try {
      const orders = await storage.getOrdersWithDetails();
      res.json(orders);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch orders" });
    }
  });

  // Dashboard stats
  app.get("/api/stats", async (req, res) => {
    try {
      const chefs = await storage.getUsersByRole("chef");
      const customers = await storage.getUsersByRole("customer");
      const dishes = await storage.getDishes();
      const orders = await storage.getOrders();
      
      const activeOrders = orders.filter(order => order.status !== "completed").length;
      const monthlyRevenue = orders
        .filter(order => order.createdAt && order.createdAt.getMonth() === new Date().getMonth())
        .reduce((sum, order) => sum + parseFloat(order.totalAmount), 0);

      res.json({
        activeChefs: chefs.filter(chef => chef.isActive).length,
        totalCustomers: customers.length,
        activeDishes: dishes.length,
        pendingOrders: activeOrders,
        monthlyRevenue: monthlyRevenue.toFixed(2)
      });
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch stats" });
    }
  });

  // Simulate real-time vote updates
  setInterval(() => {
    storage.getDishes().then(dishes => {
      dishes.forEach(async dish => {
        if ((dish.currentVotes || 0) < dish.minOrders && Math.random() > 0.8) {
          const newVotes = (dish.currentVotes || 0) + 1;
          await storage.updateDishVotes(dish.id, newVotes);
          
          broadcast({
            type: 'voteUpdate',
            dishId: dish.id,
            currentVotes: newVotes,
            progress: (newVotes / dish.minOrders) * 100,
            canStartCooking: newVotes >= dish.minOrders
          });
        }
      });
    });
  }, 15000); // Update every 15 seconds

  return httpServer;
}
