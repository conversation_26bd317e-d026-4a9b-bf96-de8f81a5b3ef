import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { X, Upload } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import type { InsertDish } from "@shared/schema";

interface AddDishModalProps {
  chefId: number;
  onClose: () => void;
}

export default function AddDishModal({ chefId, onClose }: AddDishModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    price: "",
    minOrders: "",
    cookingTime: "",
    region: "",
    category: "",
    image: "https://images.unsplash.com/photo-1603133872878-684f208fb84b?ixlib=rb-4.0.3&w=400&h=250&fit=crop"
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const createDishMutation = useMutation({
    mutationFn: async (dishData: InsertDish) => {
      const response = await apiRequest("POST", "/api/dishes", dishData);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/dishes"] });
      queryClient.invalidateQueries({ queryKey: [`/api/chefs/${chefId}/dishes`] });
      toast({
        title: "Success",
        description: "Dish added successfully!",
      });
      onClose();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to add dish. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.description || !formData.price || !formData.minOrders) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    createDishMutation.mutate({
      chefId,
      name: formData.name,
      description: formData.description,
      price: formData.price,
      image: formData.image,
      region: formData.region || "Unknown",
      cookingTime: parseInt(formData.cookingTime) || 30,
      minOrders: parseInt(formData.minOrders),
      category: formData.category || "Main Course",
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)] w-full max-w-md">
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-xl font-bold">Add New Dish</h3>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Dish Name *</label>
              <Input
                type="text"
                placeholder="e.g., Traditional Iromba"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="bg-[var(--primary-dark)] border-[var(--border-gray)]"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Description *</label>
              <Textarea
                placeholder="Describe your dish..."
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="bg-[var(--primary-dark)] border-[var(--border-gray)]"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Price ($) *</label>
                <Input
                  type="number"
                  step="0.01"
                  placeholder="12.50"
                  value={formData.price}
                  onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                  className="bg-[var(--primary-dark)] border-[var(--border-gray)]"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Min Orders *</label>
                <Input
                  type="number"
                  placeholder="20"
                  value={formData.minOrders}
                  onChange={(e) => setFormData({ ...formData, minOrders: e.target.value })}
                  className="bg-[var(--primary-dark)] border-[var(--border-gray)]"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Cooking Time (min)</label>
                <Input
                  type="number"
                  placeholder="45"
                  value={formData.cookingTime}
                  onChange={(e) => setFormData({ ...formData, cookingTime: e.target.value })}
                  className="bg-[var(--primary-dark)] border-[var(--border-gray)]"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Region</label>
                <Input
                  type="text"
                  placeholder="e.g., Manipur"
                  value={formData.region}
                  onChange={(e) => setFormData({ ...formData, region: e.target.value })}
                  className="bg-[var(--primary-dark)] border-[var(--border-gray)]"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Category</label>
              <Input
                type="text"
                placeholder="e.g., Main Course"
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                className="bg-[var(--primary-dark)] border-[var(--border-gray)]"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Dish Image</label>
              <div className="border-2 border-dashed border-[var(--border-gray)] rounded-lg p-4 text-center">
                <Upload className="w-8 h-8 text-[var(--text-secondary)] mx-auto mb-2" />
                <p className="text-[var(--text-secondary)] text-sm">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-[var(--text-secondary)] mt-1">
                  Using default image for demo
                </p>
              </div>
            </div>

            <div className="flex space-x-3 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onClose}
                className="flex-1 btn-secondary"
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                className="flex-1 btn-primary"
                disabled={createDishMutation.isPending}
              >
                {createDishMutation.isPending ? "Adding..." : "Add Dish"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
