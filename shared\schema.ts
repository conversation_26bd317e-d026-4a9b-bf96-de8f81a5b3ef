import { pgTable, text, serial, integer, boolean, decimal, jsonb, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  role: text("role").notNull().default("customer"), // customer, chef, admin
  email: text("email").notNull(),
  name: text("name").notNull(),
  avatar: text("avatar"),
  region: text("region"),
  rating: decimal("rating", { precision: 3, scale: 2 }).default("0"),
  totalOrders: integer("total_orders").default(0),
  isActive: boolean("is_active").default(true),
});

export const dishes = pgTable("dishes", {
  id: serial("id").primary<PERSON>ey(),
  chefId: integer("chef_id").notNull(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  image: text("image").notNull(),
  region: text("region").notNull(),
  cookingTime: integer("cooking_time").notNull(), // in minutes
  minOrders: integer("min_orders").notNull(),
  currentVotes: integer("current_votes").default(0),
  isActive: boolean("is_active").default(true),
  category: text("category").notNull(),
  // Enhanced fields for dish management
  tags: jsonb("tags").$type<string[]>(), // Array of tags
  status: text("status").notNull().default("draft"), // draft, active, inactive
  ingredients: jsonb("ingredients").$type<string[]>(), // Array of ingredients
  allergens: jsonb("allergens").$type<string[]>(), // Array of allergens
  dietaryInfo: jsonb("dietary_info").$type<{
    vegetarian: boolean;
    vegan: boolean;
    glutenFree: boolean;
    spicy: boolean;
  } | null>(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const dishCategories = pgTable("dish_categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(),
  description: text("description"),
  icon: text("icon"),
  color: text("color"),
  isActive: boolean("is_active").default(true),
});

export const votes = pgTable("votes", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  dishId: integer("dish_id").notNull(),
  customization: jsonb("customization"), // spice level, allergies, etc.
  createdAt: timestamp("created_at").defaultNow(),
});

export const orders = pgTable("orders", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  dishId: integer("dish_id").notNull(),
  chefId: integer("chef_id").notNull(),
  status: text("status").notNull().default("pending"), // pending, cooking, ready, delivering, completed
  totalAmount: decimal("total_amount", { precision: 10, scale: 2 }).notNull(),
  customization: jsonb("customization"),
  deliveryAddress: text("delivery_address"),
  estimatedDelivery: timestamp("estimated_delivery"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  rating: true,
  totalOrders: true,
  isActive: true,
});

export const insertDishSchema = createInsertSchema(dishes).omit({
  id: true,
  currentVotes: true,
  isActive: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  tags: z.array(z.string()).optional(),
  ingredients: z.array(z.string()).optional(),
  allergens: z.array(z.string()).optional(),
  dietaryInfo: z.object({
    vegetarian: z.boolean(),
    vegan: z.boolean(),
    glutenFree: z.boolean(),
    spicy: z.boolean(),
  }).optional(),
});

export const insertDishCategorySchema = createInsertSchema(dishCategories).omit({
  id: true,
  isActive: true,
});

export const insertVoteSchema = createInsertSchema(votes).omit({
  id: true,
  createdAt: true,
});

export const insertOrderSchema = createInsertSchema(orders).omit({
  id: true,
  createdAt: true,
});

export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Dish = typeof dishes.$inferSelect;
export type InsertDish = z.infer<typeof insertDishSchema>;
export type DishCategory = typeof dishCategories.$inferSelect;
export type InsertDishCategory = z.infer<typeof insertDishCategorySchema>;
export type Vote = typeof votes.$inferSelect;
export type InsertVote = z.infer<typeof insertVoteSchema>;
export type Order = typeof orders.$inferSelect;
export type InsertOrder = z.infer<typeof insertOrderSchema>;

// Extended types for UI
export type DishWithChef = Dish & {
  chef: User;
  voteProgress: number;
  canStartCooking: boolean;
};

export type OrderWithDetails = Order & {
  dish: Dish;
  chef: User;
  customer: User;
};

// Enhanced dish types for management
export interface DishFilter {
  category?: string;
  region?: string;
  priceRange?: { min: number; max: number };
  cookingTime?: { min: number; max: number };
  dietary?: string[];
  tags?: string[];
  status?: string;
  chefId?: number;
}

export interface DishSearchParams {
  q?: string;
  category?: string;
  region?: string;
  minPrice?: number;
  maxPrice?: number;
  minCookingTime?: number;
  maxCookingTime?: number;
  dietary?: string[];
  tags?: string[];
  status?: string;
  chefId?: number;
  limit?: number;
  offset?: number;
}
