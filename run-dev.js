// Simple development server runner
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Set environment variables
process.env.NODE_ENV = 'development';
process.env.DATABASE_URL = 'postgresql://postgres:password@localhost:5432/cloudkitchen';
process.env.JWT_SECRET = 'YourSuperSecretJWTKeyHere123456789';
process.env.SESSION_SECRET = 'YourSuperSecretSessionKeyHere123456789';
process.env.PORT = '5000';

// Run tsx with the server file
const serverPath = join(__dirname, 'server', 'index.ts');
const child = spawn('npx', ['tsx', serverPath], {
  stdio: 'inherit',
  shell: true,
  env: process.env
});

child.on('error', (error) => {
  console.error('Failed to start server:', error);
});

child.on('exit', (code) => {
  console.log(`Server exited with code ${code}`);
});
