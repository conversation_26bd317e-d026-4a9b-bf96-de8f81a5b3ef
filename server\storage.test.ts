import { storage } from './storage';
import { type InsertUser, type InsertDish, type InsertVote, type InsertOrder } from '@shared/schema';

describe('Storage', () => {
  beforeEach(() => {
    // Reset storage before each test
    jest.clearAllMocks();
  });

  describe('User Operations', () => {
    it('should create and retrieve a user', async () => {
      const newUser: InsertUser = {
        username: 'testuser',
        password: 'password123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'customer',
        region: 'Test Region'
      };

      const createdUser = await storage.createUser(newUser);
      expect(createdUser).toBeDefined();
      expect(createdUser.username).toBe(newUser.username);
      expect(createdUser.email).toBe(newUser.email);

      const retrievedUser = await storage.getUser(createdUser.id);
      expect(retrievedUser).toEqual(createdUser);
    });

    it('should get users by role', async () => {
      const chefs = await storage.getUsersByRole('chef');
      expect(chefs.length).toBeGreaterThan(0);
      expect(chefs.every(chef => chef.role === 'chef')).toBe(true);
    });
  });

  describe('Dish Operations', () => {
    it('should create and retrieve a dish', async () => {
      const newDish: InsertDish = {
        chefId: 1,
        name: 'Test Dish',
        description: 'A test dish',
        price: '15.99',
        image: 'test.jpg',
        region: 'Test Region',
        cookingTime: 30,
        minOrders: 5,
        category: 'Main Course'
      };

      const createdDish = await storage.createDish(newDish);
      expect(createdDish).toBeDefined();
      expect(createdDish.name).toBe(newDish.name);
      expect(createdDish.price).toBe(newDish.price);

      const retrievedDish = await storage.getDish(createdDish.id);
      expect(retrievedDish).toEqual(createdDish);
    });

    it('should get dishes with chef information', async () => {
      const dishesWithChef = await storage.getDishesWithChef();
      expect(dishesWithChef.length).toBeGreaterThan(0);
      expect(dishesWithChef[0].chef).toBeDefined();
    });
  });

  describe('Vote Operations', () => {
    it('should create a vote and update dish vote count', async () => {
      const newVote: InsertVote = {
        userId: 1,
        dishId: 1,
        customization: { spiceLevel: 'medium' }
      };

      const createdVote = await storage.createVote(newVote);
      expect(createdVote).toBeDefined();
      expect(createdVote.userId).toBe(newVote.userId);
      expect(createdVote.dishId).toBe(newVote.dishId);

      const dish = await storage.getDish(newVote.dishId);
      expect(dish?.currentVotes).toBeGreaterThan(0);
    });

    it('should check if user has voted', async () => {
      const hasVoted = await storage.hasUserVoted(1, 1);
      expect(typeof hasVoted).toBe('boolean');
    });
  });

  describe('Order Operations', () => {
    it('should create and retrieve an order', async () => {
      const newOrder: InsertOrder = {
        userId: 1,
        dishId: 1,
        chefId: 1,
        status: 'pending',
        totalAmount: '15.99',
        customization: { spiceLevel: 'medium' },
        deliveryAddress: '123 Test St'
      };

      const createdOrder = await storage.createOrder(newOrder);
      expect(createdOrder).toBeDefined();
      expect(createdOrder.status).toBe(newOrder.status);
      expect(createdOrder.totalAmount).toBe(newOrder.totalAmount);

      const retrievedOrder = await storage.getOrdersByUser(newOrder.userId);
      expect(retrievedOrder.length).toBeGreaterThan(0);
      expect(retrievedOrder[0].id).toBe(createdOrder.id);
    });

    it('should update order status', async () => {
      const orderId = 1;
      const newStatus = 'cooking';
      await storage.updateOrderStatus(orderId, newStatus);
      
      const orders = await storage.getOrders();
      const updatedOrder = orders.find(order => order.id === orderId);
      expect(updatedOrder?.status).toBe(newStatus);
    });
  });
}); 