import { useEffect, useRef } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";

export function useWebSocket() {
  const wsRef = useRef<WebSocket | null>(null);
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    const connect = () => {
      const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
      const wsUrl = `${protocol}//${window.location.host}/ws`;
      
      try {
        wsRef.current = new WebSocket(wsUrl);

        wsRef.current.onopen = () => {
          console.log("WebSocket connected");
        };

        wsRef.current.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            
            switch (data.type) {
              case 'voteUpdate':
                // Invalidate dishes query to trigger refresh
                queryClient.invalidateQueries({ queryKey: ["/api/dishes"] });
                
                // Show vote update notification
                toast({
                  title: "Vote Update! 🗳️",
                  description: `${data.currentVotes} votes received. ${data.votesNeeded > 0 ? `${data.votesNeeded} more needed` : 'Threshold reached!'}`,
                });
                
                if (data.thresholdReached) {
                  toast({
                    title: "🎉 Threshold Reached!",
                    description: "The dish has reached its minimum order threshold and cooking will begin soon!",
                  });
                }
                break;
                
              case 'thresholdReached':
                queryClient.invalidateQueries({ queryKey: ["/api/dishes"] });
                queryClient.invalidateQueries({ queryKey: ["/api/orders"] });
                
                toast({
                  title: "🚀 Cooking Starting Soon!",
                  description: data.message,
                });
                break;
                
              case 'dishCreated':
                queryClient.invalidateQueries({ queryKey: ["/api/dishes"] });
                toast({
                  title: "New Dish Added! 🍽️",
                  description: `A chef has added a new dish to the menu.`,
                });
                break;
                
              case 'orderCreated':
                queryClient.invalidateQueries({ queryKey: ["/api/orders"] });
                toast({
                  title: "Order Created! 📦",
                  description: "Your order has been created and is being prepared.",
                });
                break;
                
              case 'orderStatusUpdate':
                queryClient.invalidateQueries({ queryKey: ["/api/orders"] });
                toast({
                  title: "Order Update! 📋",
                  description: `Your order status has been updated to: ${data.status}`,
                });
                break;
                
              default:
                console.log("Unknown WebSocket message type:", data.type);
            }
          } catch (error) {
            console.error("Error parsing WebSocket message:", error);
          }
        };

        wsRef.current.onclose = () => {
          console.log("WebSocket disconnected");
          // Attempt to reconnect after 3 seconds
          reconnectTimeoutRef.current = setTimeout(connect, 3000);
        };

        wsRef.current.onerror = (error) => {
          console.error("WebSocket error:", error);
        };
      } catch (error) {
        console.error("Failed to create WebSocket connection:", error);
        // Attempt to reconnect after 5 seconds
        reconnectTimeoutRef.current = setTimeout(connect, 5000);
      }
    };

    connect();

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [queryClient, toast]);

  return wsRef.current;
}
