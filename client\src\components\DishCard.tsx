import React from 'react';
import { <PERSON>, CardContent, CardHeader } from './ui/card';
import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { type DishWithChef } from '@shared/schema';

interface DishCardProps {
  dish: DishWithChef;
  onVote: (dishId: number) => void;
  className?: string;
}

export const DishCard: React.FC<DishCardProps> = ({ dish, onVote, className = '' }) => {
  const progress = ((dish.currentVotes || 0) / dish.minOrders) * 100;
  const isReady = (dish.currentVotes || 0) >= dish.minOrders;

  return (
    <Card className={`overflow-hidden transition-all hover:shadow-lg ${className}`}>
      <div className="relative">
        <img
          src={dish.image}
          alt={dish.name}
          className="w-full h-48 object-cover"
        />
        <div className="absolute top-2 right-2">
          <Badge variant={isReady ? "default" : "secondary"}>
            {isReady ? "Ready to Cook" : `${dish.minOrders - (dish.currentVotes || 0)} votes needed`}
          </Badge>
        </div>
      </div>
      
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900">{dish.name}</h3>
            <p className="text-sm text-gray-600 mt-1">{dish.description}</p>
          </div>
          <div className="text-right ml-4">
            <p className="text-xl font-bold text-green-600">${dish.price}</p>
            <p className="text-sm text-gray-500">{dish.cookingTime} mins</p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Chef Info */}
        <div className="flex items-center mb-4">
          <Avatar className="w-8 h-8 mr-2">
            <AvatarImage src={dish.chef?.avatar || undefined} alt={dish.chef?.name || 'Chef'} />
            <AvatarFallback>{dish.chef?.name?.charAt(0) || 'C'}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-900">{dish.chef?.name || 'Unknown Chef'}</p>
            <p className="text-xs text-gray-500">⭐ {dish.chef?.rating || '4.0'}</p>
          </div>
        </div>

        {/* Vote Progress */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>Vote Progress</span>
            <span>{(dish.currentVotes || 0)}/{dish.minOrders} votes</span>
          </div>
          <Progress 
            value={dish.voteProgress || progress} 
            className="h-2" 
            aria-valuenow={dish.voteProgress || progress}
          />
        </div>

        {/* Vote Button */}
        <Button
          onClick={() => onVote(dish.id)}
          disabled={dish.canStartCooking || isReady}
          className="w-full"
          variant={(dish.canStartCooking || isReady) ? "outline" : "default"}
        >
          {dish.canStartCooking ? "Cooking in Progress" : isReady ? "Ready to Cook" : "Vote for this dish"}
        </Button>

        {/* Tags */}
        {dish.tags && dish.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {dish.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {dish.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{dish.tags.length - 3} more
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 