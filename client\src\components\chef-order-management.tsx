import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Clock, MapPin, ChefHat, Star, Package, CheckCircle, Users, AlertCircle } from "lucide-react";
import { useWebSocket } from "@/hooks/use-websocket";
import { useToast } from "@/hooks/use-toast";
import type { OrderWithDetails } from "@shared/schema";
import { ORDER_STATUSES } from "@/lib/constants";

interface ChefOrderManagementProps {
  chefId: number;
}

export default function ChefOrderManagement({ chefId }: ChefOrderManagementProps) {
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  // Real-time WebSocket connection
  useWebSocket();

  // Fetch chef orders
  const { data: orders = [], isLoading } = useQuery<OrderWithDetails[]>({
    queryKey: [`/api/chefs/${chefId}/orders`],
  });

  // Update order status mutation
  const updateOrderStatus = useMutation({
    mutationFn: async ({ orderId, status }: { orderId: number; status: string }) => {
      const response = await fetch(`/api/orders/${orderId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update order status');
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/chefs/${chefId}/orders`] });
      toast({
        title: "Order Status Updated! 📋",
        description: "The order status has been successfully updated.",
      });
    },
    onError: () => {
      toast({
        title: "Update Failed! ❌",
        description: "Failed to update order status. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Filter orders based on status
  const filteredOrders = statusFilter === "all" 
    ? orders 
    : orders.filter(order => order.status === statusFilter);

  // Group orders by status for tabs
  const pendingOrders = orders.filter(order => order.status === "pending");
  const cookingOrders = orders.filter(order => order.status === "cooking");
  const readyOrders = orders.filter(order => order.status === "ready");
  const deliveringOrders = orders.filter(order => order.status === "delivering");
  const completedOrders = orders.filter(order => order.status === "completed");

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-500";
      case "cooking":
        return "bg-orange-500";
      case "ready":
        return "bg-blue-500";
      case "delivering":
        return "bg-purple-500";
      case "completed":
        return "bg-green-500";
      case "cancelled":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="w-4 h-4" />;
      case "cooking":
        return <ChefHat className="w-4 h-4" />;
      case "ready":
        return <Package className="w-4 h-4" />;
      case "delivering":
        return <MapPin className="w-4 h-4" />;
      case "completed":
        return <CheckCircle className="w-4 h-4" />;
      case "cancelled":
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) return "N/A";
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getNextStatus = (currentStatus: string) => {
    const statusIndex = ORDER_STATUSES.indexOf(currentStatus as any);
    if (statusIndex < ORDER_STATUSES.length - 1) {
      return ORDER_STATUSES[statusIndex + 1];
    }
    return currentStatus;
  };

  const handleStatusUpdate = (orderId: number, currentStatus: string) => {
    const nextStatus = getNextStatus(currentStatus);
    updateOrderStatus.mutate({ orderId, status: nextStatus });
  };

  const getEstimatedDelivery = (order: OrderWithDetails) => {
    if (order.estimatedDelivery) {
      return formatDate(order.estimatedDelivery);
    }
    
    if (!order.createdAt) {
      return "N/A";
    }
    
    // Calculate estimated delivery based on cooking time and status
    const cookingTime = order.dish.cookingTime || 30;
    const orderDate = new Date(order.createdAt);
    const estimatedTime = new Date(orderDate.getTime() + (cookingTime * 60 * 1000));
    
    return formatDate(estimatedTime);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-[var(--secondary-dark)] rounded-lg p-6">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gray-600 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-600 rounded mb-2 w-32"></div>
                  <div className="h-3 bg-gray-600 rounded mb-1 w-24"></div>
                  <div className="h-3 bg-gray-600 rounded w-20"></div>
                </div>
                <div className="text-right">
                  <div className="h-4 bg-gray-600 rounded mb-1 w-16"></div>
                  <div className="h-3 bg-gray-600 rounded w-20"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-5 gap-4">
        <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-500 mb-1">{pendingOrders.length}</div>
            <div className="text-sm text-[var(--text-secondary)]">Pending</div>
          </CardContent>
        </Card>
        <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-500 mb-1">{cookingOrders.length}</div>
            <div className="text-sm text-[var(--text-secondary)]">Cooking</div>
          </CardContent>
        </Card>
        <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-500 mb-1">{readyOrders.length}</div>
            <div className="text-sm text-[var(--text-secondary)]">Ready</div>
          </CardContent>
        </Card>
        <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-500 mb-1">{deliveringOrders.length}</div>
            <div className="text-sm text-[var(--text-secondary)]">Delivering</div>
          </CardContent>
        </Card>
        <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-500 mb-1">{completedOrders.length}</div>
            <div className="text-sm text-[var(--text-secondary)]">Completed</div>
          </CardContent>
        </Card>
      </div>

      {/* Order Management Tabs */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="all">All Orders</TabsTrigger>
          <TabsTrigger value="pending">Pending ({pendingOrders.length})</TabsTrigger>
          <TabsTrigger value="cooking">Cooking ({cookingOrders.length})</TabsTrigger>
          <TabsTrigger value="ready">Ready ({readyOrders.length})</TabsTrigger>
          <TabsTrigger value="delivering">Delivering ({deliveringOrders.length})</TabsTrigger>
          <TabsTrigger value="completed">Completed ({completedOrders.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <OrderList 
            orders={filteredOrders} 
            onStatusUpdate={handleStatusUpdate}
            getStatusColor={getStatusColor}
            getStatusIcon={getStatusIcon}
            formatDate={formatDate}
            getEstimatedDelivery={getEstimatedDelivery}
          />
        </TabsContent>

        <TabsContent value="pending" className="mt-6">
          <OrderList 
            orders={pendingOrders} 
            onStatusUpdate={handleStatusUpdate}
            getStatusColor={getStatusColor}
            getStatusIcon={getStatusIcon}
            formatDate={formatDate}
            getEstimatedDelivery={getEstimatedDelivery}
          />
        </TabsContent>

        <TabsContent value="cooking" className="mt-6">
          <OrderList 
            orders={cookingOrders} 
            onStatusUpdate={handleStatusUpdate}
            getStatusColor={getStatusColor}
            getStatusIcon={getStatusIcon}
            formatDate={formatDate}
            getEstimatedDelivery={getEstimatedDelivery}
          />
        </TabsContent>

        <TabsContent value="ready" className="mt-6">
          <OrderList 
            orders={readyOrders} 
            onStatusUpdate={handleStatusUpdate}
            getStatusColor={getStatusColor}
            getStatusIcon={getStatusIcon}
            formatDate={formatDate}
            getEstimatedDelivery={getEstimatedDelivery}
          />
        </TabsContent>

        <TabsContent value="delivering" className="mt-6">
          <OrderList 
            orders={deliveringOrders} 
            onStatusUpdate={handleStatusUpdate}
            getStatusColor={getStatusColor}
            getStatusIcon={getStatusIcon}
            formatDate={formatDate}
            getEstimatedDelivery={getEstimatedDelivery}
          />
        </TabsContent>

        <TabsContent value="completed" className="mt-6">
          <OrderList 
            orders={completedOrders} 
            onStatusUpdate={handleStatusUpdate}
            getStatusColor={getStatusColor}
            getStatusIcon={getStatusIcon}
            formatDate={formatDate}
            getEstimatedDelivery={getEstimatedDelivery}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

interface OrderListProps {
  orders: OrderWithDetails[];
  onStatusUpdate: (orderId: number, currentStatus: string) => void;
  getStatusColor: (status: string) => string;
  getStatusIcon: (status: string) => React.ReactNode;
  formatDate: (date: Date | null) => string;
  getEstimatedDelivery: (order: OrderWithDetails) => string;
}

function OrderList({ 
  orders, 
  onStatusUpdate, 
  getStatusColor, 
  getStatusIcon, 
  formatDate, 
  getEstimatedDelivery 
}: OrderListProps) {
  if (orders.length === 0) {
    return (
      <Card className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
        <CardContent className="p-8 text-center">
          <Package className="w-12 h-12 mx-auto mb-4 text-[var(--text-secondary)] opacity-50" />
          <h4 className="text-lg font-semibold mb-2">No Orders Found</h4>
          <p className="text-[var(--text-secondary)]">
            No orders match the current filter.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {orders.map((order) => (
        <Card key={order.id} className="bg-[var(--secondary-dark)] border-[var(--border-gray)]">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <img
                  src={order.dish.image}
                  alt={order.dish.name}
                  className="w-16 h-16 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h4 className="font-semibold text-lg">{order.dish.name}</h4>
                    <Badge className={getStatusColor(order.status)}>
                      {getStatusIcon(order.status)}
                      <span className="ml-1">
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </span>
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-[var(--text-secondary)] mb-2">
                    <div className="flex items-center">
                      <Users className="w-4 h-4 mr-1" />
                      <span>Customer: {order.customer.name}</span>
                    </div>
                    <div className="flex items-center">
                      <Star className="w-4 h-4 mr-1 text-yellow-400 fill-current" />
                      <span>{order.customer.rating?.toString() || 'N/A'}</span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-1" />
                      <span>{order.dish.region}</span>
                    </div>
                  </div>

                  {Boolean(order.customization && typeof order.customization === 'object' && !Array.isArray(order.customization) && order.customization !== null) && (
                    <div className="text-sm text-[var(--text-secondary)] mb-2">
                      <span className="font-medium">Customizations:</span>
                      <span className="ml-1">
                        {Object.entries(order.customization as Record<string, any>)
                          .map(([key, value]) => `${key}: ${value}`)
                          .join(', ')}
                      </span>
                    </div>
                  )}

                  <div className="text-sm text-[var(--text-secondary)]">
                    <span>Ordered: {order.createdAt ? formatDate(order.createdAt) : 'N/A'}</span>
                    {order.status !== "completed" && order.status !== "cancelled" && (
                      <span className="ml-4">
                        Estimated delivery: {getEstimatedDelivery(order)}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              <div className="text-right">
                <div className="text-xl font-bold text-[var(--accent-green)] mb-2">
                  ${order.totalAmount}
                </div>
                <div className="text-sm text-[var(--text-secondary)] mb-3">
                  Order #{order.id}
                </div>
                
                {order.status !== "completed" && order.status !== "cancelled" && (
                  <Button 
                    onClick={() => onStatusUpdate(order.id, order.status)}
                    className="btn-primary"
                    disabled={order.status === "delivering"}
                  >
                    {order.status === "pending" && "Start Cooking"}
                    {order.status === "cooking" && "Mark Ready"}
                    {order.status === "ready" && "Start Delivery"}
                    {order.status === "delivering" && "Mark Delivered"}
                  </Button>
                )}
              </div>
            </div>

            {/* Order Progress Timeline */}
            {order.status !== "completed" && order.status !== "cancelled" && (
              <div className="mt-4 pt-4 border-t border-[var(--border-gray)]">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Order Received</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      ['cooking', 'ready', 'delivering', 'completed'].includes(order.status) 
                        ? 'bg-green-500' 
                        : 'bg-gray-400'
                    }`}></div>
                    <span>Cooking</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      ['ready', 'delivering', 'completed'].includes(order.status) 
                        ? 'bg-green-500' 
                        : 'bg-gray-400'
                    }`}></div>
                    <span>Ready</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      ['delivering', 'completed'].includes(order.status) 
                        ? 'bg-green-500' 
                        : 'bg-gray-400'
                    }`}></div>
                    <span>Delivering</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      order.status === 'completed' 
                        ? 'bg-green-500' 
                        : 'bg-gray-400'
                    }`}></div>
                    <span>Delivered</span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
} 