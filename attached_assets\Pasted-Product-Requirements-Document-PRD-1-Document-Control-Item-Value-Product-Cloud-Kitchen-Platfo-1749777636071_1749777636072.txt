Product Requirements Document (PRD)

1. Document Control

Item

Value

Product

Cloud Kitchen Platform for Rare Regional Dishes

Author

ChatGPT (Raj)

Version

0.9 – Draft for review

Last Updated

12 Jun 2025

Status

Draft – awaiting stakeholder sign‑off

2. Purpose

Create a two‑sided marketplace that empowers home chefs—especially housewives from under‑represented Indian regions (Manipur, Nagaland, Arunachal Pradesh, Bihar)—to sell authentic dishes in Australia. Customers vote/pre‑order; dishes are only cooked when a threshold is reached, ensuring profitability and minimal waste. The product must deliver a world‑class UX comparable to Uber Eats/Zomato while meeting Australian food‑safety and data‑privacy regulations.

3. Scope

Phase 1 (MVP): Core browse → vote → cook → deliver loop for one metro region (Sydney).

Phase 2: Advanced AI logistics, loyalty, multi‑city, and chef subscription tiers.

4. Goals & Success Metrics

Goal

KPI

Target (12 mo post‑launch)

Validate market demand

Registered customers

≥ 25 k

Empower chefs

Active chefs/month

≥ 250

Efficient batch cooking

Avg. dish threshold met rate

≥ 70 %

Delivery performance

On‑time deliveries

≥ 95 %

Customer satisfaction

App rating

≥ 4.6 / 5

Revenue

Monthly GMV

≥ A$250 k

5. <PERSON><PERSON> (32, Sydney CBD) – Food ExplorerCraves authentic home‑style meals; enjoys supporting local talent.

<PERSON><PERSON> (40, Parramatta) – Home ChefSkilled Nagaland cook; wants flexible income and recognition.

<PERSON> (28) – Delivery RiderSeeks predictable routes and fair pay.

<PERSON><PERSON> (45) – Compliance AdminEnsures food‑safety documents are current.

6. User Stories (illustrative subset)

 #

As a …

I want …

So that …

Priority

 US‑001

Customer

to browse dishes by region & rating

I can find top‑rated Manipuri meals

P0

 US‑002

Customer

to vote or pre‑order a dish

I help it reach the cook threshold

P0

 US‑003

Customer

to see chef rating & profile

I trust the food quality

P0

 US‑004

Chef

to list a dish with min‑order threshold

I avoid unprofitable batches

P0

 US‑005

Chef

to receive alerts when threshold ≈ reached

I can plan ingredients

P0

 US‑006

Delivery rider

to get an optimized route

I deliver hot food fast

P1

 US‑007

Admin

to verify chef certificates

platform stays compliant

P0

7. Functional Requirements

7.1 Customer App

 ID

Requirement

Acceptance Criteria

Priority

 FR‑C‑01

Home Feed shows: Featured Chefs carousel (rating ≥ 4.7), Trending Dishes list

API returns chefs sorted by rating; UI displays star rating badge; tap navigates to chef profile

P0

 FR‑C‑02

Dish Detail page with live vote counter & progress bar

Progress updates in < 500 ms via WebSocket; CTA states "Vote" or "Pre‑order $X"

P0

 FR‑C‑03

Customisation modal (spice level, allergies)

Selection saved in order record

P1

 FR‑C‑04

Real‑time delivery tracking map

Polling ≤ 10 s or WebSocket; shows driver ETA

P0

7.2 Chef App

 ID

Requirement

Acceptance Criteria

Priority

 FR‑H‑01

Dish Listing wizard

Must validate required fields & min‑threshold ≥ 5

P0

 FR‑H‑02

Vote Dashboard

Updates vote tally live; “Start Cooking” enabled only if feasibility = TRUE

P0

 FR‑H‑03

Ingredient inventory

Warns if stock < batch need; blocks listing

P1

7.3 Admin Console

FR‑A‑01: Certificate management grid with expiry warnings.

FR‑A‑02: Order Feasibility screen—ingredient, profit, logistics flags.

FR‑A‑03: Delivery Ops panel with route optimiser & driver assignment.

7.4 System Workflows

Browse → Vote → Threshold Reached (event dish.threshold.met).

Feasibility Check (Lambda) sets order.status = READY_TO_COOK.

Chef Start Cooking triggers delivery.assign → driver app.

Live Tracking pushes GPS to customer & chef.

8. Non‑Functional Requirements

Category

Requirement

Performance

App home feed loads ≤ 2 s on 4G; vote action round‑trip ≤ 300 ms

Scalability

Support 10 k concurrent vote events; auto‑scale up to 20 k riders

Security

OAuth2/JWT; OWASP Top‑10 safe; data encrypted at rest & transit

Compliance

Meets FSANZ food‑safety; GDPR deletion within 30 days

Accessibility

WCAG 2.1 AA

9. Analytics & KPIs

Vote‑to‑order conversion rate

Avg. votes to reach threshold

Chef retention (30‑day active)

Delivery on‑time %

CSAT & NPS surveys

10. Milestones

 Phase

Deliverables

ETA

Discovery

UX research, wireframes approved

 30 Jun 2025

MVP Alpha

Core flows, sandbox payments

 30 Sep 2025

MVP Beta

100 pilot users, App Store TestFlight

 30 Nov 2025

GA v1.0

Public launch Sydney

 Feb 2026

Phase 2

AI routing, loyalty program

 Q3 2026

11. Dependencies

AWS account & budget approval.

Third‑party payment gateway contract.

Google Maps SDK licence.

Food‑safety legal counsel sign‑off.

12. Risks & Mitigations

Risk

Likelihood

Impact

Mitigation

Chef fails hygiene audit

Medium

High

Strict onboarding, periodic checks

Low initial user adoption

Medium

Medium

Social‑media influencer campaign

Delivery shortages peak hours

High

Medium

Surge pricing & hybrid 3rd‑party drivers

13. Open Questions

What commission % vs. subscription fee provides optimum chef uptake?

Should chefs be allowed to self‑deliver?

Insurance coverage for home kitchens—platform arrangement or chef responsibility?

14. Appendix

A1: High‑fidelity wireframes (PNG links).

A2: AWS architecture diagram (PNG link).

A3: API Contract examples (OpenAPI 3 snippets).

A4: Glossary (e.g., "Threshold", "Feasibility Engine").

Next Step: Review with product, engineering, compliance, and founding team → capture feedback and iterate to v1.0 PRD.

15. Replit Agent – Front‑End Build Instructions (Android Prototype)

15.1 Context

These instructions are for an AI coding agent running inside Replit to deliver a clickable Android prototype that demonstrates the core customer & chef journeys defined in Sections 6–7 of this PRD. All back‑end calls must be mocked (local JSON / in‑memory data) so the app runs offline. Focus on:

Customer screens: Home Feed, Dish Detail + Vote/Pre‑order modal, Progress Bar, Real‑Time Tracking stub.

Chef screens: Dish Listing Wizard, Vote Dashboard, Start‑Cooking flow.

Re‑usable components (rating badge, dish card, chef avatar).

15.2 Tech Stack & Project Skeleton

Layer

Choice

Rationale

Language

Kotlin

Modern, first‑class Android support

UI Framework

Jetpack Compose 1.6

Declarative; faster iteration in Replit IDE

Architecture

MVVM + Hilt DI + Coroutines/Flow

Clear separation, testable, async streams for mocked WebSocket

Navigation

Compose Navigation

Single‑activity, type‑safe routes

Mock Networking

Retrofit + OkHttp MockWebServer

Simulate REST endpoints /dishes, /votes, /orders

JSON

Kotlinx Serialization

Lightweight, multiplatform

Image Loading

Coil

Async, Compose‑friendly

Directory Layout

app/
 └─ src/main/
     ├─ java/com/cloudkitchen/
     │   ├─ di/            # Hilt modules
     │   ├─ model/         # data classes Dish, Chef, VoteProgress
     │   ├─ network/       # Retrofit API + Mock interceptors
     │   ├─ repository/    # Interfaces + FakeRepository impl
     │   ├─ ui/
     │   │   ├─ screens/
     │   │   │   ├─ home/
     │   │   │   ├─ detail/
     │   │   │   ├─ chef/
     │   │   │   └─ track/
     │   │   ├─ components/ # DishCard, RatingChip, ProgressBar
     │   │   └─ NavGraph.kt
     │   └─ CloudKitchenApp.kt
     └─ assets/
         ├─ dishes.json
         ├─ chefs.json
         └─ votes.json

15.3 Implementation Milestones

 Step

Agent Task

Acceptance Criteria

 1

Bootstrap project using Android‑Kotlin (Gradle) template in Replit. Configure Compose, Hilt, Retrofit dependencies in build.gradle.

Project builds & runs empty Compose activity on emulator.

 2

Define data models (Dish, Chef, VoteStatus, DeliveryStatus). Annotate with @Serializable. Create sample‑*.json in assets/.

Unit test deserialises JSON into models.

 3

Set up FakeRepository implementing DishRepository, ChefRepository, returning Flow of data with delay to mimic network latency.

HomeFeed shows hard‑coded list of 5 dishes.

 4

Compose UI Components: DishCard, ChefCarousel, StarRatingChip, VoteProgressBar.

Components preview correctly in Compose Preview.

 5

HomeFeed Screen: Display Featured Chefs carousel (rating ≥ 4.7) and Traditional Dishes list (FR‑C‑01). OnClick navigates to DishDetail.

Scrollable list; tapping dish navigates.

 6

DishDetail Screen: Show image, description, chef rating, live vote counter (increment every 2 s via mocked Flow), CTA button toggles between “Vote” and “Pre‑order $X”.

Counter animates; CTA updates.

 7

Vote/Pre‑order Flow: On tap, open bottom‑sheet modal to confirm spice level & allergies (dropdowns). Persist choice in VoteStore (in‑memory). Update counter.

Snackbar “Vote recorded”.

 8

Chef Dashboard: List own dishes with vote count. If votes >= threshold, enable Start Cooking.

Clicking Start sets status = COOKING.

 9

Tracking Screen (Customer): After mock order, show 4‑step tracker Received → In Kitchen → Out for Delivery → Delivered with fake timers.

Progress bar auto‑advances every 5 s.

10

Navigation & Theme: Dark/Light theme switch, bottom nav bar (Home, Orders, Profile).

UX parity with modern food apps.

11

Unit Tests: Cover Repository flows and ViewModel state.

≥ 80 % line coverage for core logic.

12

CI Script: Add .replit run command ./gradlew test assembleDebug.

Replit green build.

15.4 Mock API Details

Endpoint

Method

Asset File

Sample Response

/dishes

GET

dishes.json

[ {"id":1,"name":"Iromba",…} ]

/chefs

GET

chefs.json

[ {"id":99,"name":"Awan",…} ]

/votes/{dishId}

POST

N/A (updates in‑memory)

{"dishId":1,"currentVotes":12}

/orders/{orderId}

GET

generated

{status:"OUT_FOR_DELIVERY"}

Retrofit is initialised with MockWebServer enqueued responses reading these JSON assets. Use Dispatcher to dynamically edit vote counts.

15.5 Definition of Done

All screens compile & run on Android emulator API 34 with no crashes.

UI matches wireframes (Appendix A1) within ±5 px paddings.

Home Feed → Vote → Chef Dashboard → Tracking loop works end‑to‑end with mock data.

Repo README documents build steps, mock endpoints, and next steps to wire real APIs.

15.6 Stretch Goals (Optional)

Lottie animations for vote success.

Compose Runtime LiveData observer to simulate WebSocket push.

Generate APK artefact and upload to Replit Artifacts.

Agent Reminder: Follow Kotlin coding conventions, push incremental commits, and update unit tests with each feature branch. Ping reviewer on PR merge.

