import { generateToken, verifyToken, decodeToken } from './jwt';
import type { User } from '../../shared/schema';

// Mock user for testing
const mockUser: User = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  password: 'hashedpassword',
  name: 'Test User',
  role: 'customer',
  region: 'Sydney',
  rating: '4.5',
  totalOrders: 10,
  isActive: true,
  avatar: 'https://example.com/avatar.jpg'
};

describe('JWT Utilities', () => {
  describe('generateToken', () => {
    it('should generate a valid JWT token', () => {
      const token = generateToken(mockUser);
      
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3); // JWT has 3 parts
    });

    it('should include user data in token payload', () => {
      const token = generateToken(mockUser);
      const decoded = decodeToken(token);
      
      expect(decoded).toBeDefined();
      expect(decoded?.userId).toBe(mockUser.id);
      expect(decoded?.username).toBe(mockUser.username);
      expect(decoded?.role).toBe(mockUser.role);
    });

    it('should generate different tokens for different users', () => {
      const user1 = { ...mockUser, id: 1, username: 'user1' };
      const user2 = { ...mockUser, id: 2, username: 'user2' };
      
      const token1 = generateToken(user1);
      const token2 = generateToken(user2);
      
      expect(token1).not.toBe(token2);
    });
  });

  describe('verifyToken', () => {
    it('should verify a valid token', () => {
      const token = generateToken(mockUser);
      const decoded = verifyToken(token);
      
      expect(decoded.userId).toBe(mockUser.id);
      expect(decoded.username).toBe(mockUser.username);
      expect(decoded.role).toBe(mockUser.role);
    });

    it('should throw error for invalid token', () => {
      const invalidToken = 'invalid.token.here';
      
      expect(() => {
        verifyToken(invalidToken);
      }).toThrow('Invalid token');
    });

    it('should throw error for malformed token', () => {
      const malformedToken = 'not.a.jwt.token';
      
      expect(() => {
        verifyToken(malformedToken);
      }).toThrow('Invalid token');
    });
  });

  describe('decodeToken', () => {
    it('should decode a valid token', () => {
      const token = generateToken(mockUser);
      const decoded = decodeToken(token);
      
      expect(decoded).toBeDefined();
      expect(decoded?.userId).toBe(mockUser.id);
      expect(decoded?.username).toBe(mockUser.username);
      expect(decoded?.role).toBe(mockUser.role);
    });

    it('should return null for invalid token', () => {
      const invalidToken = 'invalid.token.here';
      const decoded = decodeToken(invalidToken);
      
      expect(decoded).toBeNull();
    });

    it('should return null for malformed token', () => {
      const malformedToken = 'not.a.jwt.token';
      const decoded = decodeToken(malformedToken);
      
      expect(decoded).toBeNull();
    });
  });

  describe('Token Expiration', () => {
    it('should include expiration time in token', () => {
      const token = generateToken(mockUser);
      const decoded = decodeToken(token);
      
      expect(decoded).toBeDefined();
      expect(decoded?.exp).toBeDefined();
      expect(decoded?.iat).toBeDefined();
      
      // Token should expire in 24 hours
      const currentTime = Math.floor(Date.now() / 1000);
      const expectedExp = currentTime + (24 * 60 * 60);
      
      // Allow for small time differences
      expect(decoded!.exp).toBeGreaterThan(currentTime);
      expect(decoded!.exp).toBeLessThanOrEqual(expectedExp);
    });
  });

  describe('Role-based Tokens', () => {
    it('should handle different user roles', () => {
      const customerUser = { ...mockUser, role: 'customer' };
      const chefUser = { ...mockUser, role: 'chef' };
      const adminUser = { ...mockUser, role: 'admin' };
      
      const customerToken = generateToken(customerUser);
      const chefToken = generateToken(chefUser);
      const adminToken = generateToken(adminUser);
      
      const customerDecoded = decodeToken(customerToken);
      const chefDecoded = decodeToken(chefToken);
      const adminDecoded = decodeToken(adminToken);
      
      expect(customerDecoded?.role).toBe('customer');
      expect(chefDecoded?.role).toBe('chef');
      expect(adminDecoded?.role).toBe('admin');
    });
  });
}); 